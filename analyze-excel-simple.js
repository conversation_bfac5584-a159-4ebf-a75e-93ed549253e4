const XLSX = require('xlsx');

function analyzeExcelStructure() {
    console.log('Начинаем анализ Excel файла с помощью XLSX...');
    
    try {
        // Читаем файл
        const fileName = process.argv[2] || './ozon_template.xlsx';
        console.log(`Анализируем файл: ${fileName}`);
        const workbook = XLSX.readFile(fileName);
        
        console.log('=== АНАЛИЗ СТРУКТУРЫ EXCEL ФАЙЛА ===\n');
        console.log('Листы в файле:', workbook.SheetNames);
        
        // Анализируем каждый лист
        workbook.SheetNames.forEach((sheetName, index) => {
            console.log(`\n--- ЛИСТ ${index + 1}: "${sheetName}" ---`);
            
            const worksheet = workbook.Sheets[sheetName];
            const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');
            
            console.log(`Диапазон: ${worksheet['!ref']}`);
            console.log(`Строки: ${range.s.r + 1} - ${range.e.r + 1}`);
            console.log(`Колонки: ${range.s.c + 1} - ${range.e.c + 1}`);
            
            // Показываем первые 10 строк и 15 колонок
            console.log('\nСодержимое (первые строки):');
            
            for (let row = range.s.r; row <= Math.min(range.s.r + 9, range.e.r); row++) {
                console.log(`\nСтрока ${row + 1}:`);
                
                for (let col = range.s.c; col <= Math.min(range.s.c + 14, range.e.c); col++) {
                    const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                    const cell = worksheet[cellAddress];
                    
                    if (cell && cell.v !== undefined && cell.v !== null && cell.v !== '') {
                        let value = cell.v;
                        
                        // Обрезаем длинные значения
                        if (typeof value === 'string' && value.length > 50) {
                            value = value.substring(0, 47) + '...';
                        }
                        
                        console.log(`  ${cellAddress}: "${value}"`);
                        
                        // Проверяем на обязательные поля (со звездочкой)
                        if (typeof cell.v === 'string' && cell.v.includes('*')) {
                            console.log(`    ⭐ ОБЯЗАТЕЛЬНОЕ ПОЛЕ!`);
                        }
                    }
                }
            }
            
            // Анализируем объединенные ячейки
            if (worksheet['!merges']) {
                console.log('\n📋 Объединенные ячейки:');
                worksheet['!merges'].forEach(merge => {
                    const range = XLSX.utils.encode_range(merge);
                    console.log(`  ${range}`);
                });
            }
        });
        
        console.log('\nАнализ завершен!');
        
    } catch (error) {
        console.error('Ошибка при анализе файла:', error.message);
    }
}

// Запускаем анализ
analyzeExcelStructure();
