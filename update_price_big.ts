

import { PrismaClient } from '@prisma/client'

import XLSX from 'xlsx'

const prisma = new PrismaClient()
const _filename = './data/3updated_prices_with_cat.xlsx'
// const _filename = './data/TTO 7.xlsx'

const DISCOUNT = 50

type ProductDataRow = {
  prod_id: number | string;
  арт: string;
  оем: string;
  'стар.скид': number;
  'нов.скид': number;
  'изнач.опт.цена': number;
  'изнач.розн.цена': number;
  'промеж.опт.цена': number;
  'промеж.розн.цена': number;
  'новая.опт.цена': number;
  'нов.розн.цена': number;
};

async function main() {
  const workbook_1 = XLSX.readFile(_filename)
  const itemsData: ProductDataRow[] = XLSX.utils.sheet_to_json(workbook_1.Sheets[Object.keys(workbook_1.Sheets)[0]])

  console.log(itemsData[0]);
  
  const chunkSize = 1000
  const chunks = []

  for (let i = 0; i < itemsData.length; i += chunkSize) {
    chunks.push(itemsData.slice(i, i + chunkSize))
  }

  let totalProcessed = 0
  let totalErrors = 0

  for (const chunk of chunks) {
    console.log(`Processing chunk of ${chunk.length} items...`)
    
    const chunkResults = await Promise.allSettled(
      chunk.map(async (item) => {
        try {
          await prisma.products.update({
            data: {
              // prod_price: Number(item['изнач.розн.цена']),
              // prod_discount: Number(item['нов.скид']),
              prod_price: Number(item['изнач.розн.цена']),
              prod_discount: Number(item['стар.скид'])
            },
            where: {
              prod_id: Number(item.prod_id)
            }
          })
          return { success: true, item }
        } catch (error) {
          return {
            success: false,
            item,
            error: error instanceof Error ? error.message : String(error)
          }
        }
      })
    )

    const successful = chunkResults.filter(r => r.status === 'fulfilled' && r.value.success).length
    const failed = chunkResults.filter(r => r.status === 'fulfilled' && !r.value.success).length
    
    totalProcessed += successful
    totalErrors += failed

    // Логируем ошибки для текущего чанка
    chunkResults.forEach((result) => {
      if (result.status === 'fulfilled' && !result.value.success) {
        const { item, error } = result.value
        console.error(`Ошибка при обновлении товара prod_id=${item.prod_id}, арт=${item.арт}: ${error}`)
      }
    })

    console.log(`Чанк обработан: успешно ${successful}, ошибок ${failed}`)
  }

  console.log(`\nИтого: обработано ${totalProcessed} товаров, ошибок ${totalErrors}`)
}

main()
  .catch((e) => {
    throw e
  })
  .finally(async () => {
    console.log('all done')
    await prisma.$disconnect()
  })
