import { PrismaClient } from '@prisma/client'
import * as fs from 'fs'
import * as path from 'path'
import axios from 'axios'

const prisma = new PrismaClient()

interface ProcessedProduct {
  price: number
  _purpose: string
  size: string
  uses: string
  material: string
  analogs: string
  url: string
  images: string[]
  sku: string
  purpose: string
  purchase: number
}

async function notifyApi(prodId: number) {
  try {
    await axios.post(
      'https://api.rti-baltika.ru/cpan/products/update',
      {
        prod_id: prodId,
        fields: []
      },
      {
        headers: {
          Authorization: 'Bearer MTc5.FJ8TO6Cp3IthxjOeL0y6OZv2cXJ6PoclKaAlrGEEDIzK-xfSidswj_uD99NC'
        }
      }
    )
    console.log(`✅ API уведомлено для товара ID: ${prodId}`)
  } catch (error) {
    console.error(`❌ Ошибка уведомления API для товара ID: ${prodId}`, error)
    throw error
  }
}

async function main() {
  // Читаем JSON файл
  const rawData = fs.readFileSync(path.join(__dirname, '../data/processed_products.json'), 'utf8')
  const products: ProcessedProduct[] = JSON.parse(rawData)

  console.log(`Найдено ${products.length} товаров для загрузки`)

  let created = 0
  let errors = 0
  let apiErrors = 0

  // Обрабатываем каждый товар
  for (const product of products) {
    try {
      // Создаем товар
      const createdProduct = await prisma.products.create({
        data: {
          prod_sku: product.sku,
          prod_analogsku: product.sku,
          prod_price: product.price,
          prod_purpose: product.purpose,
          prod_size: product.size,
          prod_uses: product.uses,
          prod_material: product.material,
          prod_analogs: product.analogs,
          prod_manuf: 'SNF',
          prod_purchasing: String(product.purchase),
          prod_type: 'NA',
          prod_img: product.images?.[0]?.replace('.jpg', ''),
          prod_img_rumi: product.images?.[0]?.replace('.jpg', ''),
          prod_cat: '4', // Категория по умолчанию
          prod_discount: 20, // Скидка по умолчанию
          prod_secret: product.url,
          prod_composition: 'автоматически загружено с каталога xingtaihuayou',
          prod_count: 0, // Начальное количество
          prod_group_count: 0,
          prod_coeff: '2.3'
        }
      })

      // Уведомляем API
      try {
        await notifyApi(createdProduct.prod_id)
      } catch (apiError) {
        apiErrors++
        console.error(`❌ Ошибка API для товара ${product.sku}`)
        continue
      }

      created++
      console.log(`✅ Создан товар: ${product.sku} (ID: ${createdProduct.prod_id})`)
    } catch (error) {
      errors++
      console.error(`❌ Ошибка при создании товара ${product.sku}:`, error)
    }
  }

  console.log('\nРезультаты загрузки:')
  console.log(`✅ Успешно создано: ${created}`)
  console.log(`❌ Ошибок создания: ${errors}`)
  console.log(`❌ Ошибок API: ${apiErrors}`)
}

await main()
  .catch((e) => {
    console.error('Критическая ошибка:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
