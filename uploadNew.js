
const { PrismaClient } = require('@prisma/client')
const XLSX = require("xlsx");

const prisma = new PrismaClient()
const _filename = './data/NQK новый приход.xls'

// Наименование	Артикул(OEM)	Orpav	Размер	Тип	Брэнд	Кол-во	Цена, Розн.	Скидка, %

/* {
    'Наименование': 'Заглушка',
    'Артикул(OEM)': 'YF7NQK22554',
    Orpav: 'YF7NQK22554',
    'Размер': '140*15',
    'Тип': 'EC',
    'Брэнд': 'NQK',
    'Кол-во': 20,
    'Цена, Розн.': 500,
    'Скидка, %': 20
} */

async function main() {

    const workbook_1 = XLSX.readFile(_filename)
    const itemsData = XLSX.utils.sheet_to_json(workbook_1.Sheets[Object.keys(workbook_1.Sheets)[0]])

    let newItems = itemsData.map(item => {
        return {
            prod_purpose: item['Наименование'],
            prod_sku: item['Orpav'],
            prod_analogsku: item['Артикул(OEM)'],
            prod_size: item['Размер'],
            prod_type: item['Тип'],

            prod_manuf: item['Брэнд'],
            prod_count: item['Кол-во'],
            prod_price: item['Цена, Розн.'],
            prod_discount: item['Скидка, %'],
            prod_cat: '4',
            prod_composition: 'NQK новый приход.xls',
            prod_img: item['Артикул(OEM)'],
            prod_img_rumi: item['Артикул(OEM)'],
            prod_material: 'NBR'      
        }
    })

    const res = await prisma.products.createMany({
            data: newItems
    })
    
    console.log('res:', res)
    
}

main()
  .catch((e) => {
    throw e
  })
  .finally(async () => {
    await prisma.$disconnect()
  })