import { PrismaClient } from '@prisma/client'
import * as XLSX from 'xlsx'

const prisma = new PrismaClient()
const _filename = './data/Товары для сшивки Семён.xlsx'


async function main() {

    const workbook_1 = XLSX.readFile(_filename)
    const itemsData = XLSX.utils.sheet_to_json(workbook_1.Sheets[Object.keys(workbook_1.Sheets)[0]])

    await Promise.all(itemsData.map(async (i) => {
        await prisma.products.findFirst({
            where: {
                prod_size: {
                    equals: i['Наименование']
                },
                prod_type: {
                    equals: i['Тип']
                }
            },
            select: {
                prod_purpose: true,
                prod_size: true,
                prod_type: true,
                
            }
        })
    }))



}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })