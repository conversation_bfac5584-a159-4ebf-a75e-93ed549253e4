// Типы для шаблона Ozon на основе анализа Excel файла

export interface OzonProductTemplate {
  // Основные поля (обязательные помечены *)
  
  /** № - номер строки */
  number?: number;
  
  /** Артикул* - обязательное поле */
  sku: string;
  
  /** Название товара */
  name?: string;
  
  /** Цена, руб.* - обязательное поле */
  price: number;
  
  /** Цена до скидки, руб. */
  oldPrice?: number;
  
  /** НДС, %* - обязательное поле */
  vat: number;
  
  /** Рассрочка */
  installment?: 'Да' | 'Нет';
  
  /** Баллы за отзывы */
  reviewPoints?: 'Да' | 'Нет';
  
  /** SKU */
  ozonSku?: string;
  
  /** Штрихкод (Серийный номер / EAN) */
  barcode?: string;
  
  /** Вес в упаковке, г* - обязательное поле */
  weight: number;
  
  /** Ширина упаковки, мм* - обязательное поле */
  width: number;
  
  /** Высота упаковки, мм* - обязательное поле */
  height: number;
  
  /** Длина упаковки, мм* - обязательное поле */
  length: number;
  
  /** Ссылка на главное фото* - обязательное поле */
  mainImageUrl: string;
  
  // Дополнительные фото (P2-P14)
  /** Ссылка на фото 2 */
  imageUrl2?: string;
  
  /** Ссылка на фото 3 */
  imageUrl3?: string;
  
  /** Ссылка на фото 4 */
  imageUrl4?: string;
  
  /** Ссылка на фото 5 */
  imageUrl5?: string;
  
  /** Ссылка на фото 6 */
  imageUrl6?: string;
  
  /** Ссылка на фото 7 */
  imageUrl7?: string;
  
  /** Ссылка на фото 8 */
  imageUrl8?: string;
  
  /** Ссылка на фото 9 */
  imageUrl9?: string;
  
  /** Ссылка на фото 10 */
  imageUrl10?: string;
  
  /** Ссылка на фото 11 */
  imageUrl11?: string;
  
  /** Ссылка на фото 12 */
  imageUrl12?: string;
  
  /** Ссылка на фото 13 */
  imageUrl13?: string;
  
  /** Ссылка на фото 14 */
  imageUrl14?: string;
  
  // Дополнительные поля (видимо, характеристики товара)
  // Эти поля начинаются с колонки S и далее
  
  /** Дополнительные характеристики товара */
  additionalAttributes?: Record<string, string | number>;
}

// Маппинг колонок Excel в поля объекта
export const EXCEL_COLUMN_MAPPING = {
  A: 'number',           // №
  B: 'sku',             // Артикул*
  C: 'name',            // Название товара
  D: 'price',           // Цена, руб.*
  E: 'oldPrice',        // Цена до скидки, руб.
  F: 'vat',             // НДС, %*
  G: 'installment',     // Рассрочка
  H: 'reviewPoints',    // Баллы за отзывы
  I: 'ozonSku',         // SKU
  J: 'barcode',         // Штрихкод
  K: 'weight',          // Вес в упаковке, г*
  L: 'width',           // Ширина упаковки, мм*
  M: 'height',          // Высота упаковки, мм*
  N: 'length',          // Длина упаковки, мм*
  O: 'mainImageUrl',    // Ссылка на главное фото*
  P: 'imageUrl2',       // Ссылка на фото 2
  Q: 'imageUrl3',       // Ссылка на фото 3
  R: 'imageUrl4',       // Ссылка на фото 4
  S: 'imageUrl5',       // Ссылка на фото 5
  T: 'imageUrl6',       // Ссылка на фото 6
  U: 'imageUrl7',       // Ссылка на фото 7
  V: 'imageUrl8',       // Ссылка на фото 8
  W: 'imageUrl9',       // Ссылка на фото 9
  X: 'imageUrl10',      // Ссылка на фото 10
  Y: 'imageUrl11',      // Ссылка на фото 11
  Z: 'imageUrl12',      // Ссылка на фото 12
  AA: 'imageUrl13',     // Ссылка на фото 13
  AB: 'imageUrl14',     // Ссылка на фото 14
  // Дальше идут дополнительные характеристики
} as const;

// Обязательные поля
export const REQUIRED_FIELDS = [
  'sku',
  'price', 
  'vat',
  'weight',
  'width',
  'height',
  'length',
  'mainImageUrl'
] as const;

// Поля с валидацией
export const VALIDATION_FIELDS = {
  installment: ['Да', 'Нет'],
  reviewPoints: ['Да', 'Нет'],
  vat: ['Не облагается', '0', '10', '20'] // Возможные значения НДС
} as const;
