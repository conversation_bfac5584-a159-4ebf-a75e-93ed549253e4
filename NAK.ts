import { PrismaClient, products } from '@prisma/client'
import axios from 'axios'

import XLSX from 'xlsx'

type Item = {
  prod_uses: string
  prod_analogs: string
  prod_note: string
  prod_note_b: string
  prod_sku: string
  prod_type: string
  prod_size: string
  prod_material: string
  prod_analogsku: string
}

const prisma = new PrismaClient()
const _filename = './nak_created_100324.xlsx'

async function createItem(data: { prod_id: number; fields: Partial<Item> }) {
  try {
    const res = await axios.post('https://api.rti-baltika.ru/cpan/products/update', {
      prod_id: data.prod_id,
      fields: data
    })

    // console.log('status:', res.status);
  } catch (error) {
    // console.log(error)
    throw new Error('create error ')
  }
}

async function main() {
  const workbook_1 = XLSX.readFile(_filename)
  const itemsData: Item[] = XLSX.utils.sheet_to_json(workbook_1.Sheets[Object.keys(workbook_1.Sheets)[0]])

  console.log('itemsData length:', itemsData?.length)

  let created = 0
  let updated = 0
  let i = 0

  for (const item of itemsData) {
    console.log('current: ', String(item.prod_analogsku).trim(), ', i: ', i++)

    if (!item.prod_analogsku) {
      console.log('skip')

      return false
    }

    const fnd = await prisma.products.findFirst({
      where: {
        prod_analogsku: String(item.prod_analogsku).trim()
      }
    })

    if (!fnd) {
      // если НЕТ в бд

      try {
        await createItem({
          prod_id: undefined,
          fields: {
            prod_sku: String(item.prod_sku).trim(),
            prod_analogsku: String(item.prod_analogsku).trim(),
            prod_type: String(item.prod_type).trim(),
            prod_price: 0,
            prod_material: item.prod_material,
            prod_analogs: item.prod_analogs || '',
            prod_manuf: 'NAK',
            prod_cat: '4',
            prod_discount: 20,
            prod_count: 0,
            prod_purpose: 'Сальник',
            prod_composition: '100324',
            prod_size: item.prod_size,
            prod_uses: item.prod_uses || '',
            prod_note: [item.prod_note, item.prod_note_b].filter((i) => i).join(' ')
          }
        })
      } catch (error) {
        console.log('api error, create by prisma');
        
        await prisma.products.create({
          data: {
            prod_sku: String(item.prod_sku).trim(),
            prod_analogsku: String(item.prod_analogsku).trim(),
            prod_type: String(item.prod_type).trim(),
            prod_price: 0,
            prod_material: item.prod_material,
            prod_analogs: item.prod_analogs || '',
            prod_manuf: 'NAK',
            prod_cat: '4',
            prod_discount: 20,
            prod_count: 0,
            prod_purpose: 'Сальник',
            prod_composition: '100324',
            prod_size: item.prod_size,
            prod_uses: item.prod_uses || '',
            prod_note: [item.prod_note, item.prod_note_b].filter((i) => i).join(' ')
          }
        })
      }

      console.log('created: ', item.prod_analogsku)

      created++
    } else {
      // если ЕСТЬ в бд

      await prisma.products.update({
        where: {
          prod_id: fnd.prod_id
        },
        data: {
          prod_analogs: [...(fnd.prod_analogs?.split(',') || [].filter((i) => i)), item.prod_analogs].filter((i) => i).join(','),
          //   prod_composition: fnd.prod_composition + ' | ' + 'обновлено 100324',
          prod_size: item.prod_size,
          prod_type: String(item.prod_type).trim(),
          prod_uses: item.prod_uses || '',
          prod_note: [item.prod_note, item.prod_note_b].filter((i) => i).join(' ')
        }
      })

      console.log('updated: ', item.prod_analogsku)

      updated++
    }
  }

  console.log({
    created,
    updated
  })
}

// main()
//   .catch((e) => {
//     throw e
//   })
//   .finally(async () => {
//     await prisma.$disconnect()
//   })
