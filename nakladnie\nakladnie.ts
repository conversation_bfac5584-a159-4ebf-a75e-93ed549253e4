// import { PrismaClient } from '@prisma/client'
import axios from 'axios'
import fs from 'fs/promises'
import puppeteer from 'puppeteer'

// const prisma = new PrismaClient()

const basePath = './nakladnie'
const baseUrl = 'hsttps://api.rti-baltika.ru/cpan/forms/make?key=nakladnaya&orderId='
const orderIds = [35637, 35646, 35652, 35661, 35665, 35671, 35673, 35680, 35682, 35688, 35689, 35692, 35695, 35701, 35703, 35705, 35706, 35712, 35722, 35729, 35731, 35732, 35735, 35741, 35757, 35760, 35762, 35764, 35765, 35793, 35798, 35802, 35803, 35804, 35805, 35809, 35812, 35825, 35827, 35834, 35843, 35845, 35846, 35857, 35858, 35859, 35860, 35863, 35866, 35867, 35868, 35871, 35872, 35880, 35881, 35882, 35894, 35895, 35897, 35902, 35907, 35910, 35918, 35919, 35920, 35922, 35924, 35928, 35929, 35934, 35936, 35939, 35940, 35942, 35949, 35953, 35955, 35973, 35974, 35976, 35977, 35978, 35979, 35981, 35985, 35990, 36001, 36008, 36009]

async function fetchAndSaveOrders() {
  for (const orderId of orderIds) {
    try {
      // Fetch the HTML content
      const response = await axios.get(`${baseUrl}${orderId}`)
      const htmlContent = response.data.html

      // Save HTML to file
      // await fs.writeFile(`${basePath}/order_${orderId}.html`, htmlContent)

      // Convert HTML to PDF
      const browser = await puppeteer.launch()
      const page = await browser.newPage()
      await page.setContent(htmlContent)
      await page.pdf({ path: `${basePath}/order_${orderId}.pdf`, format: 'A4' })
      await browser.close()

      console.log(`Order ${orderId} processed successfully`)

      // Optional: Save order information to database using Prisma
      // await prisma.order.upsert({
      //   where: { id: orderId },
      //   update: { processed: true },
      //   create: { id: orderId, processed: true }
      // })

    } catch (error) {
      console.error(`Error processing order ${orderId}:`, error)
    }
  }
}

fetchAndSaveOrders()
  .catch((error) => console.error('Error in main execution:', error))
  .finally(async () => {
    // await prisma.$disconnect()
  })
