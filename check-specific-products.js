const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkSpecificProducts() {
  try {
    console.log('🔍 Проверяем конкретные товары...');
    
    const productIds = [528423, 27497, 26482, 26439, 27283];
    
    const products = await prisma.products.findMany({
      where: {
        prod_id: {
          in: productIds
        }
      },
      select: {
        prod_id: true,
        prod_sku: true,
        prod_note: true,
        prod_price: true,
        prod_weight: true,
        prod_images: true,
        size_in: true,
        size_out: true,
        size_h: true
      }
    });

    console.log(`✅ Найдено товаров: ${products.length}`);
    
    products.forEach((product, index) => {
      console.log(`\n${index + 1}. ID: ${product.prod_id}`);
      console.log(`   SKU: ${product.prod_sku}`);
      console.log(`   Название: ${product.prod_note?.substring(0, 100)}...`);
      console.log(`   Цена: ${product.prod_price}`);
      console.log(`   Вес: ${product.prod_weight}`);
      console.log(`   Размеры: ${product.size_in}×${product.size_out}×${product.size_h}`);
      
      // Проверяем изображения детально
      console.log(`   Изображения (raw): ${product.prod_images?.substring(0, 200)}...`);
      
      if (product.prod_images) {
        try {
          const images = JSON.parse(product.prod_images);
          console.log(`   Изображения (parsed): ${Array.isArray(images) ? images.length : 'не массив'}`);
          if (Array.isArray(images)) {
            images.slice(0, 3).forEach((img, i) => {
              console.log(`     ${i + 1}: ${img?.substring(0, 50)}...`);
            });
          }
        } catch (e) {
          console.log(`   Изображения: ошибка парсинга - ${e.message}`);
        }
      } else {
        console.log(`   Изображения: поле пустое`);
      }
    });
    
  } catch (error) {
    console.error('❌ Ошибка:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkSpecificProducts();
