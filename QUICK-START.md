# Быстрый старт - Заполнение шаблона Ozon

## Что нужно

1. Файл `ozon_template.xlsx` в корне проекта ✅
2. База данных с товарами ✅
3. Node.js и установленные зависимости ✅

## Как использовать

### 1. Проверьте товары в базе
```bash
node test-products.js
```

Скрипт покажет первые 10 товаров и предложит ID для тестирования.

### 2. Запустите заполнение шаблона
```bash
node ozon-template-filler.js 7416,7443,7478
```

Замените ID на нужные вам товары.

### 3. Получите результат
Скрипт создаст файл вида `ozon_filled_2025-09-08T08-57-14.xlsx`

## Что заполняется автоматически

### Обязательные поля ⭐
- **Артикул** ← `prod_sku`
- **Цена** ← `prod_price`
- **НДС** ← 20% (фиксированно)
- **Вес** ← `prod_weight` (кг → граммы)
- **Размеры** ← `size_in`, `size_h`, `size_out` (см → мм)
- **Главное фото** ← первое из `prod_images`

### Дополнительные поля
- **Название** ← `prod_note` (очищается от HTML)
- **Рассрочка** ← "Нет"
- **Баллы за отзывы** ← "Да"
- **Доп. фото** ← остальные из `prod_images` (до 13 штук)

## Примеры команд

```bash
# Один товар
node ozon-template-filler.js 123

# Несколько товаров
node ozon-template-filler.js 1,2,3,4,5

# Много товаров
node ozon-template-filler.js 100,101,102,103,104,105,106,107,108,109,110
```

## Что делать если ошибки

### "Товары не найдены в базе данных"
- Проверьте правильность ID товаров
- Убедитесь, что товары существуют в таблице `products`

### "Лист 'Шаблон' не найден"
- Проверьте, что файл `ozon_template.xlsx` находится в корне проекта
- Убедитесь, что это правильный шаблон Ozon

### "Не удалось распарсить изображения"
- Поле `prod_images` должно содержать JSON массив
- Пример: `["https://example.com/image1.jpg", "https://example.com/image2.jpg"]`

## Результат

После успешного выполнения вы получите:
- Заполненный Excel файл готовый для загрузки в Ozon
- Статистику обработки товаров
- Предупреждения о возможных проблемах

Файл можно сразу загружать в личный кабинет Ozon!

---

**Готово!** Скрипт максимально простой и топорный, как и требовалось 😊
