# Быстрый старт - Заполнение шаблона Ozon

## Что нужно

1. ~~Файл `ozon_template.xlsx` в корне проекта~~ НЕ НУЖЕН!
2. База данных с товарами ✅
3. Node.js и установленные зависимости ✅

## Как использовать

### 1. Проверьте товары в базе
```bash
node check-specific-products.js
```

Или проверьте любые товары:
```bash
node test-products.js
```

### 2. Запустите заполнение шаблона
```bash
node ozon-final.js 528423,27497,26482,26439,27283
```

Замените ID на нужные вам товары.

### 3. Получите результат
Скрипт создаст файл вида `ozon_final_2025-09-08T14-55-07.xlsx`

## Что заполняется автоматически

### Обязательные поля ⭐
- **Артикул** ← `prod_sku`
- **Цена** ← `prod_price`
- **НДС** ← 20% (фиксированно)
- **Вес** ← `prod_weight` (кг → граммы)
- **Размеры** ← `size_in`, `size_h`, `size_out` (см → мм)
- **Главное фото** ← первое из `prod_images`

### Дополнительные поля
- **Название** ← `prod_note` (очищается от HTML)
- **Рассрочка** ← "Нет"
- **Баллы за отзывы** ← "Да"
- **Доп. фото** ← остальные из `prod_images` (до 13 штук)

## Примеры команд

```bash
# Один товар
node ozon-final.js 123

# Несколько товаров
node ozon-final.js 1,2,3,4,5

# Много товаров
node ozon-final.js 100,101,102,103,104,105,106,107,108,109,110

# Тестовые товары (которые мы проверили)
node ozon-final.js 528423,27497,26482,26439,27283
```

## Что делать если ошибки

### "Товары не найдены в базе данных"
- Проверьте правильность ID товаров
- Убедитесь, что товары существуют в таблице `products`

### "Не удалось обработать изображения"
- Поле `prod_images` может содержать:
  - JSON массив: `["image1.jpg", "image2.jpg"]`
  - Простую строку: `"image.jpg"`
- Скрипт автоматически формирует полные URL

## Результат

После успешного выполнения вы получите:
- 📄 **Красивый Excel файл** с правильным форматированием
- 📊 **Подробную статистику** обработки товаров
- ⚠️ **Предупреждения** о возможных проблемах
- 🎯 **Все обязательные поля** заполнены правильно

### Пример вывода:
```
✅ ГОТОВО! Файл сохранен: ./ozon_final_2025-09-08T14-55-07.xlsx

📊 СТАТИСТИКА:
   ✅ Успешно обработано: 5
   ❌ С критическими ошибками: 0
   ⚠️  С предупреждениями: 0
   📦 Всего товаров: 5

🎉 ВСЕ ТОВАРЫ ГОТОВЫ ДЛЯ ЗАГРУЗКИ В OZON!
```

Файл можно сразу загружать в личный кабинет Ozon!

---

**Готово!** Теперь скрипт работает быстро и создает красивые файлы! 🎉
