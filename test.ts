function inchesToMm(str = '') {
  const regex = /\d+(?:\.\d+)?/g

  return str.replace(regex, (match) => {
    let res = Number(match) * 25.4
    // return res.toFixed(1) //.toFixed(Number.isInteger(match) ? 0 : 1)

    return Number.isInteger(Number(res.toFixed(1))) ? res.toFixed(0) : res.toFixed(1)
  })
}

function mmToInches(str = '') {
  const regex = /\d+(?:\.\d+)?/g

  return str.replace(regex, (match) => {
    let res = Number(match) / 25.4
    return res.toFixed(Number.isInteger(match) ? 0 : 3)
  })
}

const examples = [
  // format
  '0.225*0.609*0.221',
  '0.563*1.063*0.250/0.468',
  '0.745*1.497*0.250/0.270',
  '1.843*2.748/3.091*0.425/0.709'
]
// examples.forEach((example) => {
//   const mm = inchesToMm(example)
//   console.log(example, ' - ', mm)
//   console.log('обратно: ', mm, ' - ', mmToInches(mm))
// })
const mmSizes = [
  '10*20*2.7',
  '10*20*5',
  '10*20*5',
  '10*20*6',
  '10*20*7',
  '10*20*7/7.5',
  '10*20*9.5',
  '10*22*6',
  '10*22*6/6.5',
  '10*22*7',
  '10*24*7',
  '10*24*8',
  '10*25*7',
  '10*26*4',
  '10*26*7',
  '10*26*7',
  '10*27*7',
  '10*30*7',
  '10*32*2.7/5.5',
  '10*32*5',
  '100*110*5',
  '100*120*10',
  '100*120*10',
  '100*120*11',
  '100*120*12',
  '100*120*12/12.5',
  '100*120*13/18',
  '100*120*5.5',
  '100*120*9.5',
  '100*120/158*11.5/15',
  '100*122*5.5/7.5',
  '100*125*12',
  '100*125*12',
  '100*125*13',
  '100*125*13.3/21'
]
const inchSizes = mmSizes.map((size) => mmToInches(size))
const mmSizesAgain = inchSizes.map((size) => inchesToMm(size))
inchSizes.forEach((inchSize, i) => {
  const mmSize = mmSizes[i]
  const mmSizeAgain = mmSizesAgain[i]
  if (mmSize === mmSizeAgain) {
    console.log(`Размер совпал: ${mmSize}`)
  } else {
    console.log(`Ошибка конвертации: ${mmSize} => ${inchSize} => ${mmSizeAgain}`)
  }
})

const numbers = [...mmSizes, ...examples].map((str) => str.match(/\d+(?:\.\d+)?/g))

// console.log(numbers)
// [ ['100', '120', '12'], ['100', '120', '12', '12.5'], etc ]
