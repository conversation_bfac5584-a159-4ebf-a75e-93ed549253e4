import { Prisma, PrismaClient } from '@prisma/client'
import axios from 'axios'

const prisma = new PrismaClient()

async function updateItem({ fields, prod_id }: { fields: any; prod_id: any }) {
  return await axios.post('https://api.rti-baltika.ru/cpan/products/update', {
    prod_id,
    fields
  })
}

async function init() {

  const activeCats = await prisma.cats.findMany({
    select: {
      cat_id: true,
    },
    where: {
      cat_active: true
    }
  })

  const products = await prisma.products.findMany({
    select: {
      prod_id: true,
      prod_img: true
    },
    where: {
      prod_cat : {
        in: activeCats.map(c => String(c.cat_id))
      }
    },
    orderBy: {
      prod_id: 'desc'
    }
    // take: 1
  })

  console.log('len:', products.length)

  let i = 1
  for (const product of products) {
    try {
      await updateItem({
        prod_id: product.prod_id,
        fields: {
          prod_img: 'кат',
          prod_img_rumi: 'кат'
        }
      })
      console.log('id:', product.prod_id)

      console.log(`${i++} из ${products.length}`)
      console.log('-------')
    } catch (error) {
      console.log('🚀 ~ file: generatePics.ts:55 ~ init ~ error:', error)
      console.log('error id:', product.prod_id)
    }
  }
}

init()
