
const { PrismaClient } = require('@prisma/client')
const XLSX = require("xlsx");


const prisma = new PrismaClient()
const _filename = './data/25-25.xlsx'


async function main() {

    const workbook_1 = XLSX.readFile(_filename)
    const itemsData = XLSX.utils.sheet_to_json(workbook_1.Sheets[Object.keys(workbook_1.Sheets)[0]])
    
    console.log('itemsData length:', itemsData?.length)

    const res = await prisma.$transaction(
        itemsData.map((item) => {
            return prisma.products.updateMany({
                data: {
                    prod_price:  Number(item['Цена(Розн)']),
                    prod_discount: Number(item['Скидка(%)'])
                },
                where: {
                    prod_analogsku: {
                        equals: String(item['Артикул(OEM)'])
                    }
                }
            })
        })
    )
    

    console.log('RES: ', res)
}

main()
  .catch((e) => {
    throw e
  })
  .finally(async () => {
    await prisma.$disconnect()
  })