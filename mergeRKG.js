
const { PrismaClient } = require('@prisma/client')
const XLSX = require("xlsx");


const prisma = new PrismaClient()
const _newfilename = 'mergeRKG_res.xlsx'


async function main() {

    let data = {}

    const prods = await prisma.products.findMany({
      where: {
        prod_cat: {
          equals: '16'
        }
      }
    })

    prods.map(item => {
      if (!data[item.prod_analogsku]) { data[item.prod_analogsku] = [] }
      data[item.prod_analogsku].push(item)
      // console.log('data length: ', data[item.prod_analogsku].length)
    })


    Object.keys(data).map(key => {
      if (data[key].length < 2) {
        delete data[key]
      } else {
        data[key].push({})
      }
    })

    // console.log('DATA:', Object.keys(data).length)

    let nd = Object.keys(data).map(key => {
      return data[key]
    })

    // console.log('nd:', nd.flat())

/*     const sortedProds = prods.sort((a, b) => {
      let _a = JSON.stringify(a)
      let _b = JSON.stringify(b)
    }) */

    
    // return false 

    const NWB = XLSX.utils.book_new()
    const NWS = XLSX.utils.json_to_sheet(nd.flat())
    XLSX.utils.book_append_sheet(NWB, NWS, '0')

    XLSX.writeFile(NWB, _newfilename)
    
}

main()
  .catch((e) => {
    throw e
  })
  .finally(async () => {
    await prisma.$disconnect()
  })