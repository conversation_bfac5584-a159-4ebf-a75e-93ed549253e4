import { PrismaClient, products } from '@prisma/client'
const prisma = new PrismaClient()

function stringToUniqArrayString(str: string = '') {
  return [...new Set(str.split(',').filter((i) => i))].join()
}

async function s() {
  const snfRings = await prisma.products.findMany({
    where: {
      prod_cat: '6',
      prod_manuf: 'SNF'
    }
  })

  console.log('🚀 ~ file: mergeRings.ts:15 ~ s ~ snfRings len:', snfRings.length)

  for (const snfProduct of snfRings) {
    const existTCSprod = await prisma.products.findFirst({
      where: {
        prod_cat: '6',
        prod_manuf: 'TCS',
        prod_size: snfProduct.prod_size
      }
    })

    if (existTCSprod) {
      const analogsValue = [
        //format
        stringToUniqArrayString(existTCSprod.prod_analogs),
        stringToUniqArrayString(snfProduct.prod_analogs)
      ]
        .filter((i) => i)
        .join()

      console.log('snf id: ', snfProduct.prod_id)
      console.log('🚀 ~ file: mergeRings.ts:31 ~ forawait ~ analogsValue:', analogsValue)

      //   return false

      const updRes = await prisma.products.update({
        where: {
          prod_id: existTCSprod.prod_id
        },
        data: {
          prod_analogs: analogsValue,
          prod_model: [
            //format
            stringToUniqArrayString(existTCSprod.prod_model),
            stringToUniqArrayString(snfProduct.prod_model)
          ]
            .filter((i) => i)
            .join()
        }
      })

      console.log('updProd: ', updRes.prod_id)
      
      const delRes = await prisma.products.delete({
        where: {
          prod_id: snfProduct.prod_id
        }
      })

      console.log('del prod: ', delRes.prod_id)

      console.log('------');
      

    }
  }
}

s()
