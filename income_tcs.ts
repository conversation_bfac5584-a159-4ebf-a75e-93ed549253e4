// const { PrismaClient } = require('@prisma/client')
// const XLSX = require('xlsx')

import { PrismaClient } from '@prisma/client'

import XLSX from 'xlsx'

const prisma = new PrismaClient()
const _filename = './data/zTCS45_060525.xlsx'
// const _filename = './data/TTO 7.xlsx'

interface FileItem {
  code: string
  qty: number
  purch: string
  price: number
  discount: number
}

async function main() {
  const workbook_1 = XLSX.readFile(_filename)
  const itemsData: FileItem[] = XLSX.utils.sheet_to_json(workbook_1.Sheets[Object.keys(workbook_1.Sheets)[0]])

  console.log('itemsData length:', itemsData?.length)

  const notfounds: any[] = []

  const res = await prisma.$transaction(
    itemsData.map((item) => {
      return prisma.products.updateMany({
        data: {
          prod_count: {
            increment: Number(item.qty)
          },
          prod_group_count: {
            increment: Number(item.qty)
          },
          // prod_price: item.price,
          // prod_discount: item.discount * 100,
          // prod_purchasing: String(item.purch).replace(',', '.')
        },
        where: {
          prod_analogsku: {
            equals: String(item.code.trim()),
          },
          prod_manuf: 'TCS'
        }
      })
    })
  )

  console.log('RES: ', res)

  console.log('notfounds: ', notfounds)
}

main()
  .catch((e) => {
    throw e
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
