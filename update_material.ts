// const { PrismaClient } = require('@prisma/client')
// const XLSX = require('xlsx')

import { PrismaClient } from '@prisma/client'

import XLSX from 'xlsx'

const prisma = new PrismaClient()
const _filename = './data/mater.xlsx'
// const _filename = './data/TTO 7.xlsx'

interface FileItem {
  code: string
  material: string
}

async function main() {
  const workbook_1 = XLSX.readFile(_filename)
  const itemsData: FileItem[] = XLSX.utils.sheet_to_json(workbook_1.Sheets[Object.keys(workbook_1.Sheets)[0]])

  const chunkSize = 1000
  const chunks = []

  for (let i = 0; i < itemsData.length; i += chunkSize) {
    chunks.push(itemsData.slice(i, i + chunkSize))
  }

  for (const chunk of chunks) {
    await prisma.$transaction(
      chunk.map((item) =>
        prisma.products.updateMany({
          data: {
            prod_material: String(item.material).trim()
          },
          where: {
            prod_analogsku: {
              equals: String(item.code.trim())
            }
          }
        })
      )
    )
    console.log(`Processed chunk of ${chunk.length} items`)
  }
}

main()
  .catch((e) => {
    throw e
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
