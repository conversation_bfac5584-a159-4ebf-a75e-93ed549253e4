/**
 * ФИНАЛЬНАЯ ВЕРСИЯ ЗАПОЛНЕНИЯ ШАБЛОНА OZON
 * 
 * Быстрая, простая и рабочая версия
 * Создает новый Excel файл с правильным форматированием
 * 
 * Использование:
 * node ozon-final.js [id1,id2,id3...]
 * 
 * Пример:
 * node ozon-final.js 528423,27497,26482,26439,27283
 */

const { PrismaClient } = require('@prisma/client');
const ExcelJS = require('exceljs');

const prisma = new PrismaClient();

// Получаем ID товаров из аргументов командной строки
const args = process.argv.slice(2);
let productIds = [];

if (args.length === 0) {
  console.log('❌ Не указаны ID товаров!');
  console.log('');
  console.log('Использование:');
  console.log('  node ozon-final.js 528423,27497,26482,26439,27283');
  console.log('  node ozon-final.js 100');
  console.log('');
  process.exit(1);
}

// Парсим ID товаров
try {
  const idsString = args[0];
  productIds = idsString.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
  
  if (productIds.length === 0) {
    throw new Error('Не удалось распарсить ID товаров');
  }
} catch (error) {
  console.log('❌ Ошибка при парсинге ID товаров:', error.message);
  console.log('Пример: node ozon-final.js 528423,27497,26482,26439,27283');
  process.exit(1);
}

console.log(`🚀 Заполняем шаблон Ozon для товаров: ${productIds.join(', ')}`);

/**
 * Очищает HTML теги из строки
 */
function cleanHtml(str) {
  if (!str) return '';
  return str
    .replace(/<[^>]*>/g, ' ')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/\s+/g, ' ')
    .trim()
    .substring(0, 500);
}

/**
 * Преобразует товар из базы в данные для шаблона
 */
function convertProduct(product, index) {
  // Парсим изображения

  // Вес в граммах
  let weight = 100;
  if (product.prod_weight) {
    const weightKg = parseFloat(product.prod_weight);
    if (!isNaN(weightKg) && weightKg > 0) {
      weight = Math.round(weightKg * 1000);
    }
  }

  // Размеры в мм
  let width = 100, height = 100, length = 100;
  
  if (product.size_in) {
    const w = parseFloat(product.size_in) * 10;
    if (!isNaN(w) && w > 0) width = Math.round(w);
  }
  
  if (product.size_h) {
    const h = parseFloat(product.size_h) * 10;
    if (!isNaN(h) && h > 0) height = Math.round(h);
  }
  
  if (product.size_out) {
    const l = parseFloat(product.size_out) * 10;
    if (!isNaN(l) && l > 0) length = Math.round(l);
  }

  // Цена
  let price = 0;
  if (product.prod_price) {
    price = parseFloat(product.prod_price);
    if (isNaN(price)) price = 0;
  }

  // Название товара (очищаем от HTML)
  const cleanName = cleanHtml(product.prod_note) || `Товар ${product.prod_sku}`;

  return {
    number: index + 1,
    sku: product.prod_sku || `PROD_${product.prod_id}`,
    name: cleanName,
    price: price,
    oldPrice: null,
    vat: 20,
    installment: 'Нет',
    reviewPoints: 'Да',
    ozonSku: null,
    barcode: null,
    weight: weight,
    width: width,
    height: height,
    length: length,
    mainImageUrl: images[0] || '',
    images: images.slice(1, 14)
  };
}

/**
 * Создает заголовки для шаблона
 */
function createHeaders(worksheet) {
  const headers = [
    '№',
    'Артикул*',
    'Название товара',
    'Цена, руб.*',
    'Цена до скидки, руб.',
    'НДС, %*',
    'Рассрочка',
    'Баллы за отзывы',
    'SKU',
    'Штрихкод (Серийный номер / EAN)',
    'Вес в упаковке, г*',
    'Ширина упаковки, мм*',
    'Высота упаковки, мм*',
    'Длина упаковки, мм*',
    'Ссылка на главное фото*',
    'Ссылка на фото 2',
    'Ссылка на фото 3',
    'Ссылка на фото 4',
    'Ссылка на фото 5',
    'Ссылка на фото 6',
    'Ссылка на фото 7',
    'Ссылка на фото 8',
    'Ссылка на фото 9',
    'Ссылка на фото 10',
    'Ссылка на фото 11',
    'Ссылка на фото 12',
    'Ссылка на фото 13',
    'Ссылка на фото 14'
  ];

  const headerRow = worksheet.getRow(4);
  headers.forEach((header, index) => {
    const cell = headerRow.getCell(index + 1);
    cell.value = header;
    
    // Красивое форматирование заголовков
    cell.font = { bold: true, color: { argb: 'FF000000' } };
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6E6FA' }
    };
    cell.border = {
      top: { style: 'thin', color: { argb: 'FF000000' } },
      left: { style: 'thin', color: { argb: 'FF000000' } },
      bottom: { style: 'thin', color: { argb: 'FF000000' } },
      right: { style: 'thin', color: { argb: 'FF000000' } }
    };
    cell.alignment = { vertical: 'middle', horizontal: 'center', wrapText: true };
  });
  
  headerRow.height = 30;
  headerRow.commit();
}

/**
 * Заполняет строку в Excel
 */
function fillRow(worksheet, rowNumber, data) {
  const row = worksheet.getRow(rowNumber);
  
  // Заполняем основные поля
  row.getCell(1).value = data.number;
  row.getCell(2).value = data.sku;
  row.getCell(3).value = data.name;
  row.getCell(4).value = data.price;
  if (data.oldPrice) row.getCell(5).value = data.oldPrice;
  row.getCell(6).value = data.vat;
  row.getCell(7).value = data.installment;
  row.getCell(8).value = data.reviewPoints;
  if (data.ozonSku) row.getCell(9).value = data.ozonSku;
  if (data.barcode) row.getCell(10).value = data.barcode;
  row.getCell(11).value = data.weight;
  row.getCell(12).value = data.width;
  row.getCell(13).value = data.height;
  row.getCell(14).value = data.length;
  row.getCell(15).value = data.mainImageUrl;
  
  // Дополнительные изображения
  data.images.forEach((imageUrl, index) => {
    if (index < 13 && imageUrl) {
      row.getCell(16 + index).value = imageUrl;
    }
  });
  
  // Форматирование строк данных
  for (let i = 1; i <= 28; i++) {
    const cell = row.getCell(i);
    cell.border = {
      top: { style: 'thin', color: { argb: 'FF000000' } },
      left: { style: 'thin', color: { argb: 'FF000000' } },
      bottom: { style: 'thin', color: { argb: 'FF000000' } },
      right: { style: 'thin', color: { argb: 'FF000000' } }
    };
    cell.alignment = { vertical: 'middle', wrapText: true };
  }
  
  row.height = 20;
  row.commit();
}

/**
 * Валидирует данные товара
 */
function validateProduct(data) {
  const errors = [];
  
  if (!data.sku) errors.push('Отсутствует артикул');
  if (!data.price || data.price <= 0) errors.push('Некорректная цена');
  if (!data.weight || data.weight <= 0) errors.push('Некорректный вес');
  if (!data.width || data.width <= 0) errors.push('Некорректная ширина');
  if (!data.height || data.height <= 0) errors.push('Некорректная высота');
  if (!data.length || data.length <= 0) errors.push('Некорректная длина');
  if (!data.mainImageUrl) errors.push('Отсутствует главное изображение');
  
  return errors;
}

/**
 * Основная функция
 */
async function main() {
  try {
    // 1. Загружаем товары из базы
    console.log('📦 Загружаем товары из базы данных...');
    const products = await prisma.products.findMany({
      where: {
        prod_id: {
          in: productIds
        }
      }
    });

    console.log(`✅ Найдено ${products.length} товаров в базе`);

    if (products.length === 0) {
      console.log('❌ Товары не найдены в базе данных');
      return;
    }

    // 2. Создаем новый Excel файл
    console.log('📄 Создаем новый Excel файл...');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Шаблон');

    // 3. Создаем заголовки
    console.log('📝 Создаем заголовки...');
    createHeaders(worksheet);

    // 4. Заполняем данные
    console.log('📝 Заполняем данные товаров...');
    let successCount = 0;
    let errorCount = 0;
    let warningCount = 0;
    
    products.forEach((product, index) => {
      console.log(`  ${index + 1}/${products.length}: ${product.prod_sku}`);
      
      const templateData = convertProduct(product, index);
      const errors = validateProduct(templateData);
      
      if (errors.length > 0) {
        console.log(`    ❌ Ошибки: ${errors.join(', ')}`);
        errorCount++;
      } else {
        successCount++;
      }
      
      // Предупреждения
      if (!templateData.mainImageUrl) {
        console.log(`    ⚠️  Нет изображения`);
        warningCount++;
      }
      
      const rowNumber = 5 + index; // Данные начинаются с 5-й строки
      fillRow(worksheet, rowNumber, templateData);
    });

    // 5. Настраиваем ширину колонок
    worksheet.columns = [
      { width: 5 },   // №
      { width: 15 },  // Артикул
      { width: 30 },  // Название
      { width: 12 },  // Цена
      { width: 12 },  // Цена до скидки
      { width: 8 },   // НДС
      { width: 10 },  // Рассрочка
      { width: 12 },  // Баллы
      { width: 10 },  // SKU
      { width: 15 },  // Штрихкод
      { width: 10 },  // Вес
      { width: 10 },  // Ширина
      { width: 10 },  // Высота
      { width: 10 },  // Длина
      { width: 35 },  // Главное фото
      { width: 35 },  // Фото 2
      { width: 35 },  // Фото 3
      { width: 35 },  // Фото 4
      { width: 35 },  // Фото 5
      { width: 35 },  // Фото 6
      { width: 35 },  // Фото 7
      { width: 35 },  // Фото 8
      { width: 35 },  // Фото 9
      { width: 35 },  // Фото 10
      { width: 35 },  // Фото 11
      { width: 35 },  // Фото 12
      { width: 35 },  // Фото 13
      { width: 35 }   // Фото 14
    ];

    // 6. Сохраняем файл
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
    const outputPath = `./ozon_final_${timestamp}.xlsx`;
    console.log('💾 Сохраняем файл...');
    await workbook.xlsx.writeFile(outputPath);

    console.log(`\n✅ ГОТОВО! Файл сохранен: ${outputPath}`);
    console.log(`\n📊 СТАТИСТИКА:`);
    console.log(`   ✅ Успешно обработано: ${successCount}`);
    console.log(`   ❌ С критическими ошибками: ${errorCount}`);
    console.log(`   ⚠️  С предупреждениями: ${warningCount}`);
    console.log(`   📦 Всего товаров: ${products.length}`);
    
    if (errorCount === 0) {
      console.log(`\n🎉 ВСЕ ТОВАРЫ ГОТОВЫ ДЛЯ ЗАГРУЗКИ В OZON!`);
    } else {
      console.log(`\n⚠️  Проверьте товары с ошибками перед загрузкой в Ozon`);
    }

  } catch (error) {
    console.error('❌ КРИТИЧЕСКАЯ ОШИБКА:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Запускаем
main().catch(console.error);
