
const { PrismaClient } = require('@prisma/client')
const XLSX = require("xlsx");


const prisma = new PrismaClient()
const _filename = './rkg_upd_res.xlsx'


async function main() {

    const workbook_1 = XLSX.readFile(_filename)
    const itemsData = XLSX.utils.sheet_to_json(workbook_1.Sheets[Object.keys(workbook_1.Sheets)[0]])
    
    console.log("🚀 ~ file: uploadRKGU.js ~ line 14 ~ main ~ itemsData", itemsData)

    
    for (const iterator of itemsData) {
      const items = await prisma.products.findFirst({
        where: {
          prod_analogsku: item['Артикул (OEM)']
        }
      })

    }
}

main()
  .catch((e) => {
    throw e
  })
  .finally(async () => {
    await prisma.$disconnect()
  })