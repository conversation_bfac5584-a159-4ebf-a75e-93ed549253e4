
const { PrismaClient } = require('@prisma/client')
const XLSX = require("xlsx");


const prisma = new PrismaClient()
const _filename = './data/TCS 42-43.xlsx'


async function main() {

    const workbook_1 = XLSX.readFile(_filename)
    const itemsData = XLSX.utils.sheet_to_json(workbook_1.Sheets[Object.keys(workbook_1.Sheets)[0]])
    
    console.log('itemsData length:', itemsData.length)

    const data = await prisma.products.findMany({
      where: {
        prod_analogsku: {
          in: itemsData.map(i => i.code)
        }
      },
      select: {
        prod_id: true,
        prod_analogsku: true
      }
    })

    let nd = []

    data.map(i => {
      if (!nd.find(si => si.prod_analogsku == i.prod_analogsku)) {
        nd.push(i)
      }
    })


    let nf = []

    itemsData.map(i => {
      if (!nd.find(x => x.prod_analogsku == i.code)) {
        nf.push(i)
      }
    })


    console.log('RES:', nf)

  }

main()
  .catch((e) => {
    throw e
  })
  .finally(async () => {
    await prisma.$disconnect()
  })