/**
 * ФИНАЛЬНЫЙ СКРИПТ ДЛЯ ЗАПОЛНЕНИЯ ШАБЛОНА OZON
 * 
 * Простой и топорный, как требовалось!
 * 
 * Использование:
 * node ozon-template-filler.js [id1,id2,id3...]
 * 
 * Примеры:
 * node ozon-template-filler.js 1,2,3,4,5
 * node ozon-template-filler.js 100
 */

const { PrismaClient } = require('@prisma/client');
const ExcelJS = require('exceljs');

const prisma = new PrismaClient();

// Получаем ID товаров из аргументов командной строки
const args = process.argv.slice(2);
let productIds = [];

if (args.length === 0) {
  console.log('❌ Не указаны ID товаров!');
  console.log('');
  console.log('Использование:');
  console.log('  node ozon-template-filler.js 1,2,3,4,5');
  console.log('  node ozon-template-filler.js 100');
  console.log('');
  process.exit(1);
}

// Парсим ID товаров
try {
  const idsString = args[0];
  productIds = idsString.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
  
  if (productIds.length === 0) {
    throw new Error('Не удалось распарсить ID товаров');
  }
} catch (error) {
  console.log('❌ Ошибка при парсинге ID товаров:', error.message);
  console.log('Пример: node ozon-template-filler.js 1,2,3,4,5');
  process.exit(1);
}

console.log(`🚀 Заполняем шаблон Ozon для товаров: ${productIds.join(', ')}`);

/**
 * Очищает HTML теги из строки
 */
function cleanHtml(str) {
  if (!str) return '';
  return str
    .replace(/<[^>]*>/g, ' ')  // Убираем HTML теги
    .replace(/&nbsp;/g, ' ')   // Убираем &nbsp;
    .replace(/\s+/g, ' ')      // Убираем лишние пробелы
    .trim()
    .substring(0, 500);        // Ограничиваем длину
}

/**
 * Преобразует товар из базы в данные для шаблона
 */
function convertProduct(product, index) {
  // Парсим изображения
  let images = [];
  try {
    if (product.prod_images) {
      const imageData = JSON.parse(product.prod_images);
      if (Array.isArray(imageData)) {
        images = imageData.filter(img => img && img.trim() !== '');
      }
    }
  } catch (e) {
    console.warn(`⚠️  Не удалось распарсить изображения для товара ${product.prod_sku}`);
  }

  // Вес в граммах
  let weight = 100; // по умолчанию
  if (product.prod_weight) {
    const weightKg = parseFloat(product.prod_weight);
    if (!isNaN(weightKg) && weightKg > 0) {
      weight = Math.round(weightKg * 1000);
    }
  }

  // Размеры в мм
  let width = 100, height = 100, length = 100;
  
  if (product.size_in) {
    const w = parseFloat(product.size_in) * 10; // переводим см в мм
    if (!isNaN(w) && w > 0) width = Math.round(w);
  }
  
  if (product.size_h) {
    const h = parseFloat(product.size_h) * 10;
    if (!isNaN(h) && h > 0) height = Math.round(h);
  }
  
  if (product.size_out) {
    const l = parseFloat(product.size_out) * 10;
    if (!isNaN(l) && l > 0) length = Math.round(l);
  }

  // Цена
  let price = 0;
  if (product.prod_price) {
    price = parseFloat(product.prod_price);
    if (isNaN(price)) price = 0;
  }

  // Название товара (очищаем от HTML)
  const cleanName = cleanHtml(product.prod_note) || `Товар ${product.prod_sku}`;

  return {
    number: index + 1,
    sku: product.prod_sku || `PROD_${product.prod_id}`,
    name: cleanName,
    price: price,
    oldPrice: null,
    vat: 20, // НДС 20%
    installment: 'Нет',
    reviewPoints: 'Да',
    ozonSku: null,
    barcode: null,
    weight: weight,
    width: width,
    height: height,
    length: length,
    mainImageUrl: images[0] || '',
    images: images.slice(1, 14) // остальные изображения
  };
}

/**
 * Заполняет строку в Excel (ExcelJS версия - сохраняет форматирование!)
 */
function fillRow(worksheet, rowNumber, data) {
  const row = worksheet.getRow(rowNumber);

  // Заполняем основные поля
  row.getCell('A').value = data.number;
  row.getCell('B').value = data.sku;
  row.getCell('C').value = data.name;
  row.getCell('D').value = data.price;
  if (data.oldPrice) row.getCell('E').value = data.oldPrice;
  row.getCell('F').value = data.vat;
  row.getCell('G').value = data.installment;
  row.getCell('H').value = data.reviewPoints;
  if (data.ozonSku) row.getCell('I').value = data.ozonSku;
  if (data.barcode) row.getCell('J').value = data.barcode;
  row.getCell('K').value = data.weight;
  row.getCell('L').value = data.width;
  row.getCell('M').value = data.height;
  row.getCell('N').value = data.length;
  row.getCell('O').value = data.mainImageUrl;

  // Дополнительные изображения
  const imageCells = ['P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB'];
  data.images.forEach((imageUrl, index) => {
    if (index < imageCells.length && imageUrl) {
      row.getCell(imageCells[index]).value = imageUrl;
    }
  });

  row.commit();
}

/**
 * Валидирует данные товара
 */
function validateProduct(data) {
  const errors = [];
  
  if (!data.sku) errors.push('Отсутствует артикул');
  if (!data.price || data.price <= 0) errors.push('Некорректная цена');
  if (!data.weight || data.weight <= 0) errors.push('Некорректный вес');
  if (!data.width || data.width <= 0) errors.push('Некорректная ширина');
  if (!data.height || data.height <= 0) errors.push('Некорректная высота');
  if (!data.length || data.length <= 0) errors.push('Некорректная длина');
  
  return errors;
}

/**
 * Основная функция
 */
async function main() {
  try {
    // 1. Загружаем товары из базы
    console.log('📦 Загружаем товары из базы данных...');
    const products = await prisma.products.findMany({
      where: {
        prod_id: {
          in: productIds
        }
      }
    });

    console.log(`✅ Найдено ${products.length} товаров в базе`);

    if (products.length === 0) {
      console.log('❌ Товары не найдены в базе данных');
      return;
    }

    // 2. Загружаем шаблон
    console.log('📄 Загружаем шаблон Excel...');
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile('./ozon_template.xlsx');

    const worksheet = workbook.getWorksheet('Шаблон');
    if (!worksheet) {
      throw new Error('Лист "Шаблон" не найден');
    }

    // 3. Заполняем данные
    console.log('📝 Заполняем данные...');
    let successCount = 0;
    let errorCount = 0;
    
    products.forEach((product, index) => {
      console.log(`  ${index + 1}/${products.length}: ${product.prod_sku}`);
      
      const templateData = convertProduct(product, index);
      const errors = validateProduct(templateData);
      
      if (errors.length > 0) {
        console.log(`    ⚠️  Ошибки: ${errors.join(', ')}`);
        errorCount++;
      } else {
        successCount++;
      }
      
      const rowNumber = 5 + index; // Данные начинаются с 5-й строки
      fillRow(worksheet, rowNumber, templateData);
    });

    // 4. Сохраняем файл
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
    const outputPath = `./ozon_filled_${timestamp}.xlsx`;
    console.log('💾 Сохраняем файл...');
    await workbook.xlsx.writeFile(outputPath);

    console.log(`✅ Готово! Файл сохранен: ${outputPath}`);
    console.log(`📊 Статистика:`);
    console.log(`   Успешно обработано: ${successCount}`);
    console.log(`   С ошибками: ${errorCount}`);
    console.log(`   Всего товаров: ${products.length}`);

  } catch (error) {
    console.error('❌ Ошибка:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// Запускаем
main().catch(console.error);
