import { PrismaClient } from '@prisma/client'
import XLSX from 'xlsx'

const prisma = new PrismaClient()
const _filename = './Zakaz_Baltika-24-07-2024.xlsx'
const exportFileName = '240724_grigoryan_res.xlsx'

interface FileItem {
  size: string
  meta: string
  code: string
  type: string
  qty: number
  products: Array<Partial<Product>>
}

interface Product {
  prod_id: number
  prod_images?: string
  prod_analogsku?: string
  prod_sku?: string
  prod_cat: string
  prod_morecats: string
  prod_price?: number
  prod_count: number
  prod_manuf?: string
  prod_note: string
  prod_year: string
  prod_model: string
  prod_type?: string
  prod_composition?: string
  prod_uses: string
  prod_size?: string
  prod_discount: number
  prod_purchasing: string
  prod_purpose?: string
  prod_analogs: string
  prod_material: string
  prod_weight?: string
  prod_group?: string
  prod_group_count: number
  prod_group_price?: string
  prod_rk: string
  prod_minalert: number
  prod_img: string
  prod_img_rumi: string
  prod_secret: string
  prod_coeff: string
  prod_cell: string
  //   prod_gtd_alert?: string
  //   prod_group_gtd_qty?: string
  //   prod_gtd_qty?: string
  created_at: Date
  size_in?: number
  size_in_2?: number
  size_out?: number
  size_out_2?: number
  size_h?: number
  size_h_2?: number
  prod_buy_limit: number
  prod_supplier: string
}

async function main() {
  const workbook_1 = XLSX.readFile(_filename)
  const itemsData: FileItem[] = XLSX.utils.sheet_to_json(workbook_1.Sheets[Object.keys(workbook_1.Sheets)[0]]).filter((i: FileItem) => i.qty)

  //   console.log("itemsData", itemsData)
  console.log('itemsData length:', itemsData?.length)

  //  await Promise.all(itemsData.map((async (item) => {
  //     item.products = await prisma.products.findMany({
  //         select: {
  //             prod_id: true,
  //             prod_manuf: true,
  //             prod_count: true,
  //             prod_analogsku: true,
  //             prod_type: true
  //         },
  //         where: {
  //             prod_analogsku: String(item.code).trim()
  //         }
  //     })
  //  })))

  async function fetchDataInChunks(itemsData, chunkSize) {
    const chunks = []
    for (let i = 0; i < itemsData.length; i += chunkSize) {
      chunks.push(itemsData.slice(i, i + chunkSize))
    }

    const result = []
    for (const chunk of chunks) {
      // result.push(
      await Promise.all(
        chunk.map(async (item) => {
          item.products = await prisma.products.findMany({
            select: {
              prod_id: true,
              prod_manuf: true,
              prod_count: true,
              prod_analogsku: true,
              prod_type: true
            },
            where: {
              OR: [
                {
                  prod_analogsku: String(item.code).trim()
                },
                {
                  prod_analogs: {
                    contains: String(item.code).trim()
                  }
                }
              ]
            }
          })
        })
      )
      // );
    }

    return result
  }

  const chunkSize = 100
  await fetchDataInChunks(itemsData, chunkSize)

  interface CartItem {
    sku: string
    brand: string
    qty: number
  }

  interface ErrorItems extends Partial<Product> {
    reason: string
  }

  const errorItems: Array<ErrorItems> = []
  const cartItems: Array<CartItem> = []

  itemsData.map((item) => {
    const { products, ...fields } = item

    if (!item.products.length) {
      errorItems.push({
        ...fields,
        reason: 'Товар не найден'
      })
    } else if (item.products.every((i) => i.prod_count < 1)) {
      errorItems.push({
        ...fields,
        reason: 'Нет в наличии'
      })
    } else {
      const tcsItem = item.products.find((x) => x.prod_manuf == 'TCS')
      if (tcsItem?.prod_count > 0) {
        cartItems.push({ sku: tcsItem.prod_analogsku, brand: tcsItem.prod_manuf, qty: item.qty })
      } else {
        item.products.sort((a, b) => b.prod_count - a.prod_count)
        const fp = item.products[0]
        cartItems.push({
          sku: fp.prod_analogsku,
          brand: fp.prod_manuf,
          qty: item.qty
        })
      }
    }
  })

  const NWB = XLSX.utils.book_new()

  XLSX.utils.book_append_sheet(NWB, XLSX.utils.json_to_sheet(cartItems), 'cart')
  XLSX.utils.book_append_sheet(NWB, XLSX.utils.json_to_sheet(errorItems), 'errors')

  XLSX.writeFile(NWB, exportFileName)
}

main()
  .catch((e) => {
    throw e
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
