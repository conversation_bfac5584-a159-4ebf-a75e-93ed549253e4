{"name": "rti-prisma", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/big.js": "^6.2.2", "@types/exceljs": "^0.5.3", "prisma": "^6.15.0", "typescript": "^5.9.2"}, "dependencies": {"@prisma/client": "^6.15.0", "axios": "^1.7.7", "big.js": "^6.2.1", "daytona-ozon-seller-api": "^2.2.17", "exceljs": "^4.4.0", "prisma-extension-nested-operations": "^1.0.0", "tsx": "^3.12.7", "xlsx": "^0.18.5"}}