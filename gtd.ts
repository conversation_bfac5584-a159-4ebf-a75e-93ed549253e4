import { Prisma, PrismaClient } from '@prisma/client'
import XLSX from 'xlsx'

const prisma = new PrismaClient()

type SpecListWithProductSelected = {
  id: number
  spec: number
  prod: string
  qty: number
  invoice: string
  np: string
  prod_analogsku: string
  prod_cat: string
}

async function getList({ orderId, specId }: { orderId: number; specId: number }) {
  const order = await prisma.orders.findUnique({
    where: {
      order_id: orderId
    }
  })

  const orderItems = await prisma.order_items.findMany({
    where: {
      items_order_id: orderId
    }
  })

  const specList = await prisma.spec_list.findMany({
    where: {
      spec: specId
    },
    orderBy: {
      qty: 'asc'
    }
  })

  // specList.forEach(item => {
  //   item.product =
  // })

  function findFirstSpecList(item: (typeof orderItems)[0], specId: number, resIds: any[]) {
    // Фильтрация массива
    // Сортировка по qty в порядке возрастания (asc)
    let filteredList = specList.toSorted((a, b) => a.qty - b.qty).filter((specItem) => specItem.qty >= item.item_count && specItem.spec == specId && !resIds.includes(specItem.prod))

    // Возвращаем первый элемент или null, если массив пуст
    return filteredList.length > 0 ? filteredList[0] : null
  }

  const spec = await prisma.specs.findUnique({
    where: {
      id: specId
    }
  })

  const orderProducts = await prisma.products.findMany({
    where: {
      prod_id: {
        in: orderItems.map((item) => item.item_id)
      }
    }
  })

  //!!! TODO: Проверять остаток веса!

  type ResItem = {
    prod_id: number
    prod: string
    originalProd?: string
    orderQty: number
    specStock: number
    specNewStock: number
    original: boolean
    spec: number | string
    invoice: string
    np: number | string
    notAvailable?: boolean
  }

  type SpecItem = (typeof specList)[0]

  const result: Array<ResItem | null | undefined> = []

  for (const item of orderItems) {
    //   const product = await prisma.products.findUnique({
    //     where: {
    //       prod_id: item.item_id
    //     },
    //     select: {
    //       prod_id: true,
    //       prod_analogsku: true,
    //       prod_sku: true
    //     }
    //   })

    const product = orderProducts.find((prod) => prod.prod_id == item.item_id)

    // console.log('current product: ', product?.prod_analogsku)

    let originalSpecProduct: ResItem | undefined | null = undefined
    let specProduct: ResItem | undefined | null = undefined

    // пробуем оригинальный товар
    if (product) {
      // originalSpecProduct = specList.find((spec) => (spec.prod == product.prod_analogsku || spec.prod == product.prod_sku) && spec.qty >= item.item_count)
      // console.log('1.current product: ', product?.prod_analogsku)

      const res = await prisma.spec_list.findFirst({
        where: {
          prod: product.prod_analogsku || '',
          qty: {
            gte: item.item_count
          },
          spec: specId
        },
        orderBy: {
          qty: 'asc'
        }
      })

      if (res) {
        originalSpecProduct = {
          invoice: res.invoice,
          np: res.np,
          prod_id: product.prod_id,
          originalProd: product.prod_analogsku,
          prod: res.prod,
          orderQty: item.item_count,
          specStock: res.qty,
          specNewStock: Number(res.qty) - Number(item.item_count),
          original: true,
          spec: res.spec
        }
      }
      if (!originalSpecProduct) {
        // specProduct = specList.find((spec) => spec.qty >= item.item_count)

        const resIds = result.map((i) => i?.prod || '')
        // console.log("🚀 ~ getList ~ resIds:", resIds)
        // console.log('🚀 ~ orderItems.map ~ resIds:', resIds)
        // const res = await prisma.spec_list.findFirst({
        //   where: {
        //     qty: {
        //       gte: item.item_count
        //     },
        //     spec: specId,
        //     prod: {
        //       notIn: resIds
        //     }
        //   },
        //   orderBy: {
        //     qty: 'asc'
        //   }
        // })

        
        const unq = [...new Set(resIds)]
        

        let res: SpecListWithProductSelected | null
        const rawres = await prisma.$queryRaw`SELECT sl.*, p.prod_analogsku, p.prod_cat
                                            FROM spec_list sl
                                            JOIN products p ON sl.prod = p.prod_analogsku
                                            WHERE sl.qty >= ${item.item_count}
                                              AND sl.spec = ${specId}
                                              AND sl.prod NOT IN (${unq.length ? Prisma.join(unq) : ''})
                                              AND p.prod_cat = ${product.prod_cat}
                                            ORDER BY sl.qty ASC
                                            LIMIT 1;`

        res = rawres[0] as SpecListWithProductSelected
 
        if (res) {
          specProduct = {
            originalProd: product?.prod_analogsku,
            prod_id: product.prod_id,
            prod: res.prod,
            orderQty: item.item_count,
            specStock: res.qty,
            specNewStock: Number(res.qty) - Number(item.item_count),
            invoice: res.invoice,
            np: res.np,
            original: false,
            spec: res.spec
          }
        } else {
          specProduct = {
            prod: product?.prod_analogsku || item.ID + ': Недоступен',
            prod_id: product.prod_id,
            orderQty: item.item_count,
            specStock: 0,
            specNewStock: 0,
            original: false,
            spec: 0,
            invoice: '',
            np: '',
            notAvailable: true
          }
        }

        specProduct && result.push(specProduct)
      }
      originalSpecProduct && result.push(originalSpecProduct)
    }

    // если не нашли, то ищем подходящий
  }

  /// write file

  const NWB = XLSX.utils.book_new()

  const NWS = XLSX.utils.json_to_sheet(result)

  XLSX.utils.book_append_sheet(NWB, NWS, 'Лист')

  XLSX.writeFile(NWB, `${orderId}-${specId}.xlsx`)

  /// -----------

  return result
}

const init = async () => {
  const res = await getList({ orderId: 33779, specId: 5 })
  console.log('🚀 ~ init ~ res:', res)
}

init()
