import { PrismaClient } from '@prisma/client'
import * as XLSX from 'xlsx'
import axios from 'axios'

const prisma = new PrismaClient()

const _filename = './data/RK GG (1).xlsx'
const exportFileName = 'rg_gg_res3.xlsx'

export interface ApiReponseProduct {
  prod_id: number
  prod_sku: string
  prod_analogsku: string
  prod_cat: string
  prod_weight: string
  prod_price: number
  prod_count: number
  prod_manuf: string
  prod_note: string
  prod_year: string
  prod_type: string
  prod_uses: string
  prod_size: string
  prod_discount: number
  prod_purpose: string
  prod_analogs: string
  prod_material: string
  prod_rk: string
  prod_group: string
  prod_group_count: number
  prod_img: string
  prod_img_rumi: string
  prod_images: string
  prod_model: string
  prod_buy_limit: number
  cat_title: string
  qty: number
  whosaleprice: number
  buy_limit: number
}

interface FileItem {
  Наличие: string
  'Артикул(OEM)': string
  Аналоги: string
  Продано: string
  Востреб: string
  prod_id?: number
  prod_purpose?: string
  prod_uses?: string
  prod_type?: string
  purpose_en?: string
  uses_en?: string
  type_en?: string
}

const translatedTypes = {
  prod_type: 'Product Type',
  'Центральный гидроблок': 'Central Hydraulic Block',
  'Central Joint': 'Central Joint',
  'PUMP KIT': 'Pump Kit',
  Bucket: 'Bucket',
  Стрела: 'Boom',
  'Цилиндр стрелы(Boom cyl)': 'Boom Cylinder',
  Boom: 'Boom',
  'Цилиндр рукояти(arm cyl)': 'Arm Cylinder',
  'STEERING KIT': 'Steering Kit',
  'ARM/Dipper': 'Arm/Dipper',
  'Цилиндр ковша(Bucket cyl)': 'Bucket Cylinder',
  Рукоять: 'Arm',
  'Поворот стрелы': 'Boom Swing',
  Отвал: 'Blade',
  Аутригер: 'Outrigger',
  'Подъем отвала': 'Blade Lift',
  Hydraulic: 'Hydraulic',
  Lift: 'Lift',
  'Ковш экскаватора': 'Excavator Bucket',
  'Стрела 4,9 м': 'Boom 4.9m',
  'Подъем отвала (морозная серия)': 'Blade Lift (Cold Series)',
  'Стрела (обратная лопата)': 'Boom (Backhoe)',
  'гидроцилиндр двухчелюстного ко': 'Hydraulic Cylinder for Two-Jaw Chuck',
  'открытие челюстей ковша 7 в 1': 'Opening of 7-in-1 Bucket Jaws',
  'Поворот колес': 'Wheel Turn',
  'Подъем рыхлителя': 'Tilt of Ripper',
  'Наклон рыхлителя': 'Tilt of Ripper',
  Рыхлитель: 'Ripper',
  'CENTER JOINT': 'Center Joint'
}

async function s() {
  const workbook_1 = XLSX.readFile(_filename)
  const itemsData: FileItem[] = XLSX.utils.sheet_to_json(workbook_1.Sheets[Object.keys(workbook_1.Sheets)[0]])

  const products = await prisma.products.findMany({
    where: {
      prod_analogsku: {
        in: itemsData.map((i) => i['Артикул(OEM)'].trim())
      }
    }
  })

  itemsData.map((item) => {
    const fp = products.find((x) => x.prod_analogsku?.trim() == item['Артикул(OEM)'].trim())

    item.prod_id = fp?.prod_id
    item.prod_purpose = fp?.prod_purpose
    item.prod_uses = fp?.prod_uses
    item.prod_type = fp?.prod_type
  })

  //   console.log('itemsData:', itemsData.filter(item => !item.prod_id))

  //   return false;

  await Promise.all(
    itemsData.map(async (item) => {
      try {
        const res = await axios(`https://rti-baltika.ru/api/product/${item.prod_id}`, {
          headers: {
            'Content-Type': 'application/json',
            'x-locale': 'en'
          }
        })

        const translatedProduct: ApiReponseProduct = res.data

        item.uses_en = translatedProduct.prod_uses
        item.purpose_en = translatedProduct.prod_purpose
        item.type_en = translatedTypes[item.prod_type] //translatedProduct.prod_type
      } catch (error) {
        console.log(error)
      }
    })
  )

  const NWB = XLSX.utils.book_new()

  XLSX.utils.book_append_sheet(NWB, XLSX.utils.json_to_sheet(itemsData), 'book1')

  XLSX.writeFile(NWB, exportFileName)
}

s()
