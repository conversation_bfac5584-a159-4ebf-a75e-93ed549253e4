import { PrismaClient, products } from '@prisma/client'

import XLSX from 'xlsx'

type Item = {
  prod_sku: string
  prod_type: string
  prod_size: string
  prod_count: string
  prod_purchasing: string | number
  prod_price: number
  prod_material: string
  prod_analogs: string
  prod_manuf: string
}

const prisma = new PrismaClient()
const _filename = './NAKE.xlsx'

async function main() {
  const workbook_1 = XLSX.readFile(_filename)
  const itemsData: Item[] = XLSX.utils.sheet_to_json(workbook_1.Sheets[Object.keys(workbook_1.Sheets)[0]])

  console.log('itemsData length:', itemsData?.length)

  let created = 0
  let updated = 0

  await Promise.all(
    itemsData.map(async (item) => {
      const fnd = await prisma.products.findFirst({
        // select: {
        //   prod_id: true
        // },
        where: {
          OR: [
            {
              prod_sku: String(item.prod_sku).trim()
            },
            {
              prod_analogsku: String(item.prod_sku).trim()
            }
          ]
        }
      })

      if (!fnd) {
        // notfounds.push(item)
        // если НЕТ в бд

        await prisma.products.create({
          data: {
            prod_sku: String(item.prod_sku).trim(),
            prod_analogsku: String(item.prod_sku).trim(),
            prod_type: String(item.prod_type).trim(),
            prod_count: isNaN(Number(item.prod_count)) ? 0 : Number(item.prod_count),
            prod_purchasing: String(item.prod_purchasing).replace(',', '.'),
            prod_price: Number(item.prod_price),
            prod_material: item.prod_material,
            prod_analogs: item.prod_analogs,
            prod_manuf: item.prod_manuf,
            prod_cat: '4',
            prod_discount: 20,
            prod_purpose: 'Сальник',
            prod_composition: 'файл NAKE.xlsx',
            prod_size: item.prod_size
          }
        })

        created++
      } else {
        // если ЕСТЬ в бд

        await prisma.products.update({
          where: {
            prod_id: fnd.prod_id
          },
          data: {
            prod_count: {
              increment: isNaN(Number(item.prod_count)) ? 0 : Number(item.prod_count)
            },
            prod_purchasing: String(item.prod_purchasing).replace(',', '.'),
            prod_price: Number(item.prod_price),//Number(fnd.prod_price) == 0 ? Number(item.prod_price) : fnd.prod_price,
            prod_analogs: [...(item.prod_analogs?.split(',') || [].filter((i) => i)), fnd.prod_analogs].join(', '),
            prod_composition: fnd.prod_composition + ' | ' + 'обновлено из файл NAKE.xlsx'
          }
        })

        updated++
      }
    })
  )

  console.log({
    created,
    updated
  })
  
}

// main()
//   .catch((e) => {
//     throw e
//   })
//   .finally(async () => {
//     await prisma.$disconnect()
//   })
