
const { PrismaClient } = require('@prisma/client')
const XLSX = require("xlsx");


const prisma = new PrismaClient()
const _newfilename = 'rkg_upd_res.xlsx'
const _filename = './data/mergeRKG_sano.xlsx'



async function main() {

    const workbook_1 = XLSX.readFile(_filename)
    const itemsData = XLSX.utils.sheet_to_json(workbook_1.Sheets[Object.keys(workbook_1.Sheets)[0]])

    const groups = {}
    // console.log("itemsData:", itemsData)
    
    itemsData.map(item => {
        
        if (!groups[item['Артикул (OEM)']] ) {
            groups[item['Артикул (OEM)']] = []
        }

        groups[item['Артикул (OEM)']].push(item)
    })


    let errors = []

    let newData = Object.keys(groups).map(key => {
        let _analogs = []
        let _models  = []
        let _qty_s = []
        let _types = []
        let _qty

        let _purpose
        let _uses

        let FI = groups[key][0]

        try {
            groups[key].forEach(item => {
                _analogs.push([...new Set([item['Артикул (OEM)'], item['Код'], ...item['Аналоги']?.split(',').filter(i => i)])])
    
                _models.push(item['Модель']) // + вырезать назначения
                
                let mhv = item['Назначение'].split(' ')[3]
    
                if (mhv) _models.push(mhv)
    
                _qty_s.push(item['Наличие'])
                _types.push(item['Тип'])
            })
        } catch (error) {
            console.log('ERROR key:', key)
            console.error(error)
            errors.push(key)
        }

            // в применении просто Hitachi
            // назначение обрезать до ремкомпл гидроц модель
            _uses = FI['Назначение'].split(' ')[2] || ''

            let _prp = FI['Назначение'].split(' ')
            _purpose = _prp[0] + ' ' + _prp[1] + ' ' + _prp[2]

        _models  = [...new Set(_models.flat())]
        _analogs = [...new Set(_analogs.flat())]
        _types   = [...new Set(_types.flat())]

        _qty = Math.max(..._qty_s.flat()) 

        return {
            'Артикул (OEM)': FI['Артикул (OEM)'],
            'Код': FI['Код'],
            'Наличие': _qty,
            'Производитель': FI['Производитель'],
            'Модель': _models.join(', '),
            'Тип': _types.join(', '),
            'Размер': FI['Размер'],
            'Назначение': _purpose,
            'Аналоги':  _analogs.join(','),
            'Применение': _uses
        }

    })

    // console.log('newData:', newData)
    console.log('Ошибок:', errors.join());

    const NWB = XLSX.utils.book_new()
    const NWS = XLSX.utils.json_to_sheet(newData)
    XLSX.utils.book_append_sheet(NWB, NWS, '0')

    XLSX.writeFile(NWB, _newfilename)
    
}

main()
  .catch((e) => {
    throw e
  })
  .finally(async () => {
    await prisma.$disconnect()
  })