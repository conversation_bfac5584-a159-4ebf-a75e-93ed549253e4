generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model adonis_schema {
  id             Int      @id @default(autoincrement()) @db.UnsignedInt
  name           String   @db.VarChar(255)
  batch          Int
  migration_time DateTime @default(now()) @db.Timestamp(0)
}

model api_tokens {
  id         Int       @id @default(autoincrement()) @db.UnsignedInt
  user_id    Int?      @db.UnsignedInt
  name       String    @db.VarChar(255)
  type       String    @db.VarChar(255)
  token      String    @db.VarChar(64)
  ip_address String?   @db.VarChar(50)
  expires_at DateTime? @db.Timestamp(0)
  created_at DateTime  @default(dbgenerated("(0000-00-00 00:00:00)")) @db.Timestamp(0)
}

model cart {
  cart_id        Int      @id @default(autoincrement())
  cart_cookie    String   @db.Text
  cart_client    String?  @db.<PERSON><PERSON><PERSON><PERSON>(50)
  cart_items     String   @db.LongText
  created_at     DateTime @default(dbgenerated("(0000-00-00 00:00:00)")) @db.Timestamp(0)
  updated_at     DateTime @default(dbgenerated("(0000-00-00 00:00:00)")) @db.Timestamp(0)
  cart_timestamp DateTime @default(dbgenerated("(0000-00-00 00:00:00)")) @db.Timestamp(0)
}

model cart_items {
  id         Int      @id @default(autoincrement()) @db.UnsignedInt
  created_at DateTime @default(now()) @db.Timestamp(0)
  updated_at DateTime @default(dbgenerated("(0000-00-00 00:00:00)")) @db.Timestamp(0)
  cart_id    Int?
  prod_id    Int?
  qty        Int?
}

model cat_columns {
  ID      Int     @id @default(autoincrement())
  keyname String  @db.VarChar(255)
  title   String  @db.VarChar(255)
  sort    Int     @default(0)
  sorted  Boolean @default(false)
  slot    Boolean @default(true)
  cat_id  Int
}

model cats {
  cat_id          Int     @unique(map: "cat_id") @default(autoincrement())
  cat_title       String  @db.VarChar(255)
  cat_pic         String  @db.VarChar(255)
  cat_sort        Int
  cat_search_sort Int
  cat_rootcat     Int?    @default(0)
  cat_active      Boolean
  cat_url_ru      String  @db.VarChar(255)
  cat_url_en      String  @db.VarChar(255)
  cat_url_pl      String  @db.VarChar(255)
  cat_url_es      String  @db.VarChar(255)
  cat_url_fr      String  @db.VarChar(255)
  cat_url_de      String  @db.VarChar(255)
  cat_url_it      String  @db.VarChar(255)
  cat_note        String? @db.VarChar(255)
  cat_base64_pic  String? @db.LongText
  imagesize       String? @db.VarChar(255)
}

model clients {
  client_id         Int      @id @default(autoincrement())
  client_number     Int?
  client_name       String   @db.VarChar(255)
  client_mail       String   @db.VarChar(100)
  client_phone      String   @db.VarChar(100)
  client_country    String   @db.VarChar(255)
  client_adress     String   @db.Text
  client_city       String   @db.VarChar(255)
  client_street     String   @db.VarChar(255)
  client_house      String   @db.VarChar(100)
  client_flat       String   @db.VarChar(100)
  client_postindex  String   @db.VarChar(255)
  client_password   String   @db.VarChar(180)
  remember_me_token String?  @db.VarChar(255)
  created_at        DateTime @default(now()) @db.Timestamp(0)
  updated_at        DateTime @default(dbgenerated("(0000-00-00 00:00:00)")) @db.Timestamp(0)
}

model filters {
  id          Int     @id @default(autoincrement()) @db.UnsignedInt
  category_id Int?
  field       String? @db.VarChar(255)
  title       String? @db.VarChar(255)
}

model lang_dict {
  id   Int    @id @default(autoincrement())
  tkey String @db.Text
  en   String @db.Text
  pl   String @db.Text
  de   String @db.Text
  es   String @db.Text
  it   String @db.Text
  fr   String @db.Text
}

model langs {
  id                   Int    @id @default(autoincrement())
  item_id              Int
  lang_prod_purpose_ru String @db.Text
  lang_prod_purpose_en String @db.Text
  lang_prod_purpose_pl String @db.Text
  lang_prod_uses_en    String @db.Text
  lang_prod_uses_pl    String @db.Text
  lang_prod_uses_ru    String @db.Text
}

model order_items {
  ID             Int      @id @default(autoincrement())
  items_order_id Int
  item_id        Int
  item_count     Int
  cancelgtd      Boolean  @default(false)
  timestamp      DateTime @default(now()) @db.Timestamp(0)
  created_at     DateTime @default(dbgenerated("(0000-00-00 00:00:00)")) @db.Timestamp(0)
  updated_at     DateTime @default(dbgenerated("(0000-00-00 00:00:00)")) @db.Timestamp(0)
}

model order_log {
  log_id       Int      @id @default(autoincrement())
  log_order_id Int
  log_title    String   @db.Text
  log_time     DateTime @default(now()) @db.Timestamp(0)
  log_user     String   @default(dbgenerated("(Гость)")) @db.VarChar(30)
}

model orders {
  order_id                Int       @id @default(autoincrement())
  order_datetime          DateTime  @default(now()) @db.Timestamp(0)
  order_lastupdate        DateTime? @db.Timestamp(0)
  order_lastupdate_person String    @db.VarChar(100)
  order_status            String    @default(dbgenerated("(Не обработан)")) @db.VarChar(20)
  order_client            String    @default(dbgenerated("(Гость)")) @db.VarChar(11)
  order_shipping          String    @db.Text
  order_payment           String    @db.Text
  order_price             Int
  order_endprice          Int
  order_shippingprice     Int
  order_desc              String    @db.VarChar(255)
  order_company           String    @db.VarChar(100)
  order_clienttype        String    @db.VarChar(20)
  order_locale            String    @db.VarChar(10)
  order_coupons           String    @db.VarChar(100)
  order_notice            String    @db.Text
  order_gtd               Boolean   @default(false)
  order_tracknumber       String    @db.VarChar(255)
}

model orders_snapshots {
  ID         Int      @id @default(autoincrement())
  orderid    Int
  date       DateTime @default(now()) @db.Timestamp(0)
  body       String   @db.LongText
  user       String   @default(dbgenerated("(client)")) @db.VarChar(50)
  created_at DateTime @default(dbgenerated("(0000-00-00 00:00:00)")) @db.Timestamp(0)
  updated_at DateTime @default(dbgenerated("(0000-00-00 00:00:00)")) @db.Timestamp(0)
}

model orgs {
  org_id     Int      @id @default(autoincrement())
  org_client String   @db.VarChar(50)
  org_name   String   @db.VarChar(255)
  org_adress String   @db.Text
  org_inn    String   @db.VarChar(255)
  org_kpp    String   @db.VarChar(255)
  org_rschet String   @db.VarChar(255)
  org_kschet String   @db.VarChar(255)
  org_bik    String   @db.VarChar(255)
  org_bank   String   @db.VarChar(255)
  org_vat    String   @db.VarChar(255)
  created_at DateTime @default(now()) @db.Timestamp(0)
  updated_at DateTime @default(dbgenerated("(0000-00-00 00:00:00)")) @db.Timestamp(0)
}

model pages {
  page_id     Int    @id @default(autoincrement())
  page_title  String @db.VarChar(255)
  page_key    String @db.VarChar(255)
  page_body   String @db.MediumText
  page_locale String @db.VarChar(3)
}

model product_log {
  id           Int      @id @default(autoincrement())
  product_id   Int
  user         String   @db.VarChar(100)
  stock_before String   @db.VarChar(11)
  stock_after  String   @db.VarChar(11)
  msg          String   @db.Text
  time         DateTime @default(now()) @db.Timestamp(0)
}

model product_schemas {
  id         Int      @id @default(autoincrement()) @db.UnsignedInt
  created_at DateTime @default(now()) @db.Timestamp(0)
  updated_at DateTime @default(dbgenerated("(0000-00-00 00:00:00)")) @db.Timestamp(0)
  product_id Int?
  body       String?  @db.MediumText
}

model products {
  prod_id            Int      @unique(map: "prod_id") @default(autoincrement())
  prod_title         String?  @db.VarChar(255)
  prod_analogsku     String?  @db.VarChar(255)
  prod_sku           String?  @db.VarChar(100)
  prod_cat           String   @db.VarChar(100)
  prod_morecats      String   @db.VarChar(100)
  prod_price         Decimal? @default(0.00) @db.Decimal(8, 2)
  prod_count         Int      @default(0)
  prod_manuf         String?  @db.Text
  prod_note          String   @db.LongText
  prod_year          String   @db.VarChar(100)
  prod_model         String   @db.Text
  prod_type          String?  @db.VarChar(30)
  prod_composition   String?  @db.Text
  prod_uses          String   @db.Text
  prod_size          String?  @db.VarChar(100)
  prod_discount      Float    @default(0) @db.Float
  prod_purchasing    String   @db.VarChar(50)
  prod_purpose       String?  @db.VarChar(250)
  prod_analogs       String   @db.Text
  prod_material      String   @db.VarChar(50)
  prod_weight        String?  @db.VarChar(50)
  prod_group         String?  @db.VarChar(50)
  prod_group_count   Int      @default(0)
  prod_group_price   String?  @db.VarChar(11)
  prod_rk            String   @db.Text
  prod_minalert      Int      @default(10)
  prod_img           String   @db.Text
  prod_img_rumi      String   @db.VarChar(100)
  prod_secret        String   @db.VarChar(2000)
  prod_coeff         String   @db.VarChar(5)
  prod_cell          String   @db.VarChar(255)
  prod_gtd_alert     String?  @db.VarChar(11)
  prod_group_gtd_qty String?  @db.VarChar(11)
  prod_gtd_qty       String?  @db.VarChar(11)
  //created_at         DateTime @default(now()) @db.Timestamp(0)
  size_in            Decimal? @db.Decimal(8, 2)
  size_in_2          Decimal? @db.Decimal(8, 2)
  size_out           Decimal? @db.Decimal(8, 2)
  size_out_2         Decimal? @db.Decimal(8, 2)
  size_h             Decimal? @db.Decimal(8, 2)
  size_h_2           Decimal? @db.Decimal(8, 2)
  prod_buy_limit     Int
}

model sessions {
  session_id String  @id @db.VarChar(128)
  expires    Int     @db.UnsignedInt
  data       String? @db.Text
}

model settings {
  s_id    Int     @id @default(autoincrement())
  s_key   String  @db.VarChar(255)
  s_value String  @db.Text
  json    Boolean @default(false)
}

model shippingprice {
  ID         Int @id @default(autoincrement())
  country_id Int
  price      Int
}

model shops {
  shop_id   Int    @id @default(autoincrement())
  shop_name String @db.VarChar(250)
}

model sms_auth {
  id         Int      @id @default(autoincrement())
  auth_code  Int
  auth_time  DateTime @default(now()) @db.Timestamp(0)
  auth_login String   @db.VarChar(255)
}

model spec_journal {
  id         Int      @id @default(autoincrement())
  spec_id    Int
  date       DateTime @default(now()) @db.Timestamp(0)
  exportdate DateTime @default(dbgenerated("(0000-00-00 00:00:00)")) @db.Timestamp(0)
  orderId    Int
  prod       String   @db.VarChar(100)
  qty        Int
  increment  Boolean  @default(false)
  transit    Int      @default(0)
}

model spec_list {
  id      Int    @id @default(autoincrement())
  spec    Int
  prod    String @db.VarChar(100)
  qty     Int
  invoice String @db.VarChar(11)
  np      String @db.VarChar(11)
}

model specs {
  id         Int      @id @default(autoincrement())
  spec_num   Int
  spec_title String   @db.Text
  spec_date  DateTime @default(now()) @db.Timestamp(0)
  active     Boolean  @default(true)
}

model statistics {
  id          Int      @id @default(autoincrement()) @db.UnsignedInt
  created_at  DateTime @default(now()) @db.Timestamp(0)
  query       String?  @db.VarChar(255)
  client_ip   String   @db.VarChar(50)
  client_info String   @db.Text
  count       Int      @default(1)
  manual      Boolean  @default(false)
}

model template_views {
  id         Int      @id @default(autoincrement()) @db.UnsignedInt
  created_at DateTime @default(now()) @db.Timestamp(0)
  updated_at DateTime @default(dbgenerated("(0000-00-00 00:00:00)")) @db.Timestamp(0)
  name       String?  @db.VarChar(255)
  template   String?  @db.LongText
}

model users {
  user_id       Int    @id @default(autoincrement())
  user_password String @db.Text
  user_name     String @db.VarChar(255)
  user_mail     String @db.VarChar(70)
  user_phone    String @db.VarChar(30)
  user_role     String @db.VarChar(10)
}
