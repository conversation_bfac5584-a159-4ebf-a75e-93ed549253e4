generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model adonis_schema {
  id             Int      @id @default(autoincrement()) @db.UnsignedInt
  name           String   @default("") @db.VarChar(255)
  batch          Int      @default(0)
  migration_time DateTime @default(now()) @db.Timestamp(0)
}

model api_tokens {
  id         Int       @id @default(autoincrement()) @db.UnsignedInt
  user_id    Int?      @default(0) @db.UnsignedInt
  name       String    @default("") @db.VarChar(255)
  type       String    @default("") @db.VarChar(255)
  token      String    @default("") @db.VarChar(64)
  ip_address String?   @default("") @db.VarChar(50)
  expires_at DateTime? @default(now()) @db.Timestamp(0)
  created_at DateTime  @default(dbgenerated("('0000-00-00 00:00:00')")) @db.Timestamp(0)
}

model cart {
  cart_id     Int      @id @default(autoincrement())
  cart_cookie String   @default("") @db.Text
  cart_client String?  @default("") @db.VarChar(50)
  cart_items  String   @default("") @db.LongText
  created_at  DateTime @default(dbgenerated("('0000-00-00 00:00:00')")) @db.Timestamp(0)
  updated_at  DateTime @default(dbgenerated("('0000-00-00 00:00:00')")) @db.Timestamp(0)

  @@index([cart_client], map: "cart_client")
  @@index([cart_cookie(length: 1024)], map: "cart_cookie")
}

model cart_items {
  id         Int      @id @default(autoincrement()) @db.UnsignedInt
  created_at DateTime @default(now()) @db.Timestamp(0)
  updated_at DateTime @default(dbgenerated("('0000-00-00 00:00:00')")) @db.Timestamp(0)
  cart_id    Int?     @default(0)
  prod_id    Int?     @default(0)
  qty        Int?     @default(0)

  @@index([cart_id], map: "cart_id")
  @@index([prod_id], map: "prod_id")
  @@index([prod_id], map: "prod_id_2")
}

model cat_columns {
  ID      Int     @id @default(autoincrement())
  keyname String  @default("") @db.VarChar(255)
  title   String  @default("") @db.VarChar(255)
  sort    Int     @default(0)
  sorted  Boolean @default(false)
  slot    Boolean @default(true)
  cat_id  Int     @default(0)

  @@index([cat_id], map: "cat_id")
}

model cats {
  cat_id          Int     @unique(map: "cat_id") @default(autoincrement())
  cat_title       String  @default("") @db.VarChar(255)
  cat_pic         String  @default("") @db.VarChar(255)
  cat_sort        Int     @default(0)
  cat_search_sort Int     @default(0)
  cat_rootcat     Int?    @default(0)
  cat_active      Boolean @default(false)
  cat_url_ru      String  @default("") @db.VarChar(255)
  cat_url_en      String  @default("") @db.VarChar(255)
  cat_url_pl      String  @default("") @db.VarChar(255)
  cat_url_es      String  @default("") @db.VarChar(255)
  cat_url_fr      String  @default("") @db.VarChar(255)
  cat_url_de      String  @default("") @db.VarChar(255)
  cat_url_it      String  @default("") @db.VarChar(255)
  cat_note        String? @default("") @db.VarChar(255)
  cat_base64_pic  String? @default("") @db.LongText
  imagesize       String? @default("") @db.VarChar(255)
  isnormal        Boolean @default(true)
  duplicate       Boolean @default(false)

  @@index([cat_rootcat], map: "cat_rootcat")
}

model clients {
  client_id         Int      @id @default(autoincrement())
  client_number     Int?     @default(0)
  client_name       String   @default("") @db.VarChar(255)
  client_mail       String   @default("") @db.VarChar(100)
  client_phone      String   @default("") @db.VarChar(100)
  client_country    String   @default("") @db.VarChar(255)
  client_adress     String   @default("") @db.Text
  client_city       String   @default("") @db.VarChar(255)
  client_street     String   @default("") @db.VarChar(255)
  client_house      String   @default("") @db.VarChar(100)
  client_flat       String   @default("") @db.VarChar(100)
  client_postindex  String   @default("") @db.VarChar(255)
  client_password   String   @default("") @db.VarChar(180)
  client_discount   Decimal  @default(0) @db.Decimal(10, 0)
  client_cdekid     String   @db.VarChar(50)
  remember_me_token String?  @default("") @db.VarChar(255)
  created_at        DateTime @default(now()) @db.Timestamp(0)
  updated_at        DateTime @default(dbgenerated("('0000-00-00 00:00:00')")) @db.Timestamp(0)

  @@index([client_mail], map: "client_mail")
  @@index([client_mail], map: "client_mail_2")
  @@index([client_number], map: "client_number")
}

model filters {
  id          Int     @id @default(autoincrement()) @db.UnsignedInt
  category_id Int?    @default(0)
  field       String? @default("") @db.VarChar(255)
  title       String? @default("") @db.VarChar(255)

  @@index([category_id], map: "category_id")
}

model lang_dict {
  id      Int     @id @default(autoincrement())
  tkey    String  @default("") @db.Text
  en      String  @default("") @db.Text
  pl      String  @default("") @db.Text
  de      String  @default("") @db.Text
  es      String  @default("") @db.Text
  it      String  @default("") @db.Text
  fr      String  @default("") @db.Text
  ar      String  @default("") @db.Text
  backend Boolean @default(false)

  @@index([tkey(length: 1024)], map: "tkey")
}

model order_items {
  ID             Int      @id @default(autoincrement())
  items_order_id Int      @default(0)
  item_id        Int      @default(0)
  item_count     Int      @default(0)
  cancelgtd      Boolean  @default(false)
  timestamp      DateTime @default(now()) @db.Timestamp(0)
  created_at     DateTime @default(dbgenerated("('0000-00-00 00:00:00')")) @db.Timestamp(0)
  updated_at     DateTime @default(dbgenerated("('0000-00-00 00:00:00')")) @db.Timestamp(0)

  @@index([item_id], map: "item_id")
  @@index([item_id], map: "item_id_2")
  @@index([items_order_id], map: "items_order_id")
}

model order_log {
  log_id       Int      @id @default(autoincrement())
  log_order_id Int      @default(0)
  log_title    String   @default("") @db.Text
  log_time     DateTime @default(now()) @db.Timestamp(0)
  log_user     String   @default("Гость") @db.VarChar(30)
}

model orders {
  order_id                Int       @id @default(autoincrement())
  order_datetime          DateTime  @default(now()) @db.Timestamp(0)
  order_lastupdate        DateTime? @default(now()) @db.Timestamp(0)
  order_lastupdate_person String    @default("") @db.VarChar(100)
  order_status            String    @default("Не обработан") @db.VarChar(20)
  order_client            String    @default("Гость") @db.VarChar(11)
  order_shipping          String    @default("") @db.Text
  order_payment           String    @default("") @db.Text
  order_price             Int       @default(0)
  order_endprice          Int       @default(0)
  order_shippingprice     Int       @default(0)
  order_desc              String    @default("") @db.VarChar(255)
  order_company           String    @default("") @db.VarChar(100)
  order_clienttype        String    @default("") @db.VarChar(20)
  order_locale            String    @default("") @db.VarChar(10)
  order_coupons           String    @default("") @db.VarChar(100)
  order_notice            String    @default("") @db.Text
  order_gtd               Boolean   @default(false)
  order_tracknumber       String    @default("") @db.VarChar(255)
  order_weight            Decimal   @db.Decimal(10, 0)

  @@index([order_client], map: "order_client")
  @@index([order_client], map: "order_client_2")
  @@index([order_payment(length: 1024)], map: "order_payment")
  @@index([order_payment(length: 1024)], map: "order_payment_2")
  @@index([order_payment(length: 1024)], map: "order_payment_3")
  @@index([order_shipping(length: 1024)], map: "order_shipping")
  @@index([order_status], map: "order_status")
}

model orders_snapshots {
  ID         Int      @id @default(autoincrement())
  orderid    Int
  date       DateTime @default(now()) @db.Timestamp(0)
  body       String   @default("") @db.LongText
  user       String   @default("client") @db.VarChar(50)
  created_at DateTime @default(dbgenerated("('0000-00-00 00:00:00')")) @db.Timestamp(0)
  updated_at DateTime @default(dbgenerated("('0000-00-00 00:00:00')")) @db.Timestamp(0)

  @@index([orderid], map: "orderid")
}

model orgs {
  org_id     Int      @id @default(autoincrement())
  org_client String   @default("") @db.VarChar(50)
  org_name   String   @default("") @db.VarChar(255)
  org_adress String   @default("") @db.Text
  org_inn    String   @default("") @db.VarChar(255)
  org_kpp    String   @default("") @db.VarChar(255)
  org_rschet String   @default("") @db.VarChar(255)
  org_kschet String   @default("") @db.VarChar(255)
  org_bik    String   @default("") @db.VarChar(255)
  org_bank   String   @default("") @db.VarChar(255)
  org_vat    String   @default("") @db.VarChar(255)
  created_at DateTime @default(now()) @db.Timestamp(0)
  updated_at DateTime @default(dbgenerated("('0000-00-00 00:00:00')")) @db.Timestamp(0)

  @@index([org_client], map: "org_client")
}

model pages {
  page_id     Int     @id @default(autoincrement())
  page_title  String  @default("") @db.VarChar(255)
  page_key    String  @default("") @db.VarChar(255)
  page_body   String  @default("") @db.MediumText
  page_locale String  @default("") @db.VarChar(3)
  print       Boolean @default(false)
}

model product_log {
  id           Int      @id @default(autoincrement())
  product_id   Int      @default(0)
  user         String   @default("") @db.VarChar(100)
  stock_before String   @default("") @db.VarChar(11)
  stock_after  String   @default("") @db.VarChar(11)
  msg          String   @default("") @db.Text
  time         DateTime @default(now()) @db.Timestamp(0)
}

model product_schemas {
  id          Int      @id @default(autoincrement()) @db.UnsignedInt
  created_at  DateTime @default(now()) @db.Timestamp(0)
  updated_at  DateTime @default(dbgenerated("('0000-00-00 00:00:00')")) @db.Timestamp(0)
  product_id  Int?     @default(0)
  related_ids String?  @db.Text
  body        String?  @db.LongText

  @@index([product_id], map: "product_id")
  @@index([related_ids(length: 1024)], map: "related_ids")
}

model products {
  prod_id            Int      @unique(map: "prod_id") @default(autoincrement())
  prod_images        String?  @default("") @db.LongText
  prod_analogsku     String?  @default("") @db.VarChar(255)
  prod_sku           String?  @default("") @db.VarChar(100)
  prod_cat           String   @default("") @db.VarChar(100)
  prod_morecats      String   @default("") @db.VarChar(100)
  prod_price         Decimal? @default(0.00) @db.Decimal(8, 2)
  prod_count         Int      @default(0)
  prod_manuf         String?  @default("") @db.Text
  prod_note          String   @default("") @db.LongText
  prod_year          String   @default("") @db.VarChar(100)
  prod_model         String   @default("") @db.Text
  prod_type          String?  @default("") @db.VarChar(255)
  prod_composition   String?  @default("") @db.Text
  prod_uses          String   @default("") @db.Text
  prod_size          String?  @default("") @db.VarChar(100)
  prod_discount      Float    @default(0) @db.Float
  prod_purchasing    String   @default("") @db.VarChar(50)
  prod_purpose       String?  @default("") @db.VarChar(250)
  prod_analogs       String   @default("") @db.Text
  prod_material      String   @default("") @db.VarChar(50)
  prod_weight        String?  @default("") @db.VarChar(50)
  prod_group         String?  @default("") @db.VarChar(50)
  prod_group_count   Int      @default(0)
  prod_rk            String   @default("") @db.Text
  prod_minalert      Int      @default(10)
  prod_img           String   @default("") @db.Text
  prod_img_rumi      String   @default("") @db.VarChar(100)
  prod_secret        String   @default("") @db.VarChar(2000)
  prod_coeff         String   @default("") @db.VarChar(5)
  prod_cell          String   @default("") @db.Text
  prod_gtd_alert     String?  @default("") @db.VarChar(11)
  prod_group_gtd_qty String?  @default("") @db.VarChar(11)
  prod_gtd_qty       String?  @default("") @db.VarChar(11)
  created_at         DateTime @default(now()) @db.Timestamp(0)
  size_in            Decimal? @default(0.00) @db.Decimal(8, 2)
  size_in_2          Decimal? @default(0.00) @db.Decimal(8, 2)
  size_out           Decimal? @default(0.00) @db.Decimal(8, 2)
  size_out_2         Decimal? @default(0.00) @db.Decimal(8, 2)
  size_h             Decimal? @default(0.00) @db.Decimal(8, 2)
  size_h_2           Decimal? @default(0.00) @db.Decimal(8, 2)
  prod_buy_limit     Int      @default(0)
  prod_supplier      String   @default("") @db.VarChar(200)

  @@index([prod_analogsku], map: "prod_analogsku")
  @@index([prod_cat], map: "prod_cat")
  @@index([prod_group], map: "prod_group")
  @@index([prod_sku], map: "prod_sku")
}

model sessions {
  session_id String  @id @db.VarChar(128)
  expires    Int     @db.UnsignedInt
  data       String? @db.Text
}

model settings {
  s_id    Int     @id @default(autoincrement())
  s_key   String  @default("") @db.VarChar(255)
  s_value String  @default("''") @db.LongText
  json    Boolean @default(false)
  syst    Boolean @default(false)
}

model shippingprice {
  ID         Int @id @default(autoincrement())
  country_id Int @default(0)
  price      Int @default(0)
}

model shops {
  shop_id   Int    @id @default(autoincrement())
  shop_name String @default("") @db.VarChar(250)
}

model sms_auth {
  id         Int      @id @default(autoincrement())
  auth_code  Int      @default(0)
  auth_time  DateTime @default(now()) @db.Timestamp(0)
  auth_login String   @default("") @db.VarChar(255)
}

model spec_journal {
  id         Int      @id @default(autoincrement())
  spec_id    Int      @default(0)
  date       DateTime @default(now()) @db.Timestamp(0)
  exportdate DateTime @default(dbgenerated("('0000-00-00 00:00:00')")) @db.Timestamp(0)
  orderId    Int      @default(0)
  prod       String   @default("") @db.VarChar(100)
  qty        Int      @default(0)
  increment  Boolean  @default(false)
  transit    Int      @default(0)
}

model spec_list {
  id      Int    @id @default(autoincrement())
  spec    Int    @default(0)
  prod    String @default("") @db.VarChar(100)
  qty     Int    @default(0)
  invoice String @default("") @db.VarChar(11)
  np      String @default("") @db.VarChar(11)
}

model specs {
  id         Int      @id @default(autoincrement())
  spec_num   Int      @default(0)
  spec_title String   @default("") @db.Text
  spec_date  DateTime @default(now()) @db.Timestamp(0)
  filepath   String   @db.Text
  active     Boolean  @default(true)
}

model statistics {
  id          Int      @id @default(autoincrement()) @db.UnsignedInt
  created_at  DateTime @default(now()) @db.Timestamp(0)
  query       String?  @default("") @db.VarChar(255)
  client_ip   String   @default("") @db.VarChar(50)
  client_info String   @default("") @db.Text
  count       Int      @default(1)
  manual      Boolean  @default(false)

  @@index([client_ip], map: "client_ip")
  @@index([client_ip], map: "client_ip_2")
  @@index([query], map: "query")
}

model template_views {
  id         Int      @id @default(autoincrement()) @db.UnsignedInt
  created_at DateTime @default(now()) @db.Timestamp(0)
  updated_at DateTime @default(dbgenerated("('0000-00-00 00:00:00')")) @db.Timestamp(0)
  name       String?  @default("") @db.VarChar(255)
  template   String?  @default("") @db.LongText
}

model users {
  user_id       Int    @id @default(autoincrement())
  user_password String @default("") @db.Text
  user_name     String @default("") @db.VarChar(255)
  user_mail     String @default("") @db.VarChar(70)
  user_phone    String @default("") @db.VarChar(30)
  user_role     String @default("") @db.VarChar(10)
}

model journals {
  id         Int      @id @default(autoincrement()) @db.UnsignedInt
  created_at DateTime @default(now()) @db.Timestamp(0)
  updated_at DateTime @default(dbgenerated("('0000-00-00 00:00:00')")) @db.Timestamp(0)
  entity     String?  @db.VarChar(255)
  entity_id  Int?
  msg        String?  @db.Text
  user_name  String?  @db.VarChar(255)
  user_id    Int?
}

model locks {
  id         Int      @id @default(autoincrement()) @db.UnsignedInt
  created_at DateTime @default(now()) @db.Timestamp(0)
  updated_at DateTime @default(dbgenerated("('0000-00-00 00:00:00')")) @db.Timestamp(0)
  entity     String?  @db.VarChar(255)
  entity_id  Int?
  user_name  String?  @db.VarChar(255)
  user_id    Int?
  active     Boolean? @default(true)
}

model telegram_sessions {
  id      Int    @id @default(autoincrement())
  user_id Int
  token   String @db.VarChar(255)
}

model cache {
  id         Int      @id @default(autoincrement())
  ckey       String   @db.Text
  hash       String   @db.Text
  body       String   @db.LongText
  updated_at DateTime @default(now()) @db.Timestamp(0)
  created_at DateTime @default(now()) @db.Timestamp(0)

  @@index([ckey(length: 768)], map: "ckey")
}

model html_chunks {
  id      Int    @id @default(autoincrement())
  keyname String @db.VarChar(255)
  title   String @db.VarChar(255)
  body    String @db.LongText
}

model passports {
  id         Int      @id @default(autoincrement())
  created_at DateTime @default(now()) @db.Timestamp(0)
  updated_at DateTime @default(dbgenerated("('0000-00-00 00:00:00')")) @db.Timestamp(0)
  client_id  Int
  body       String   @db.LongText
}

model product_snapshots {
  id         Int      @id @default(autoincrement())
  created_at DateTime @default(now()) @db.Timestamp(0)
  prod_id    Int?
  body       String?  @db.Text
  user       String?  @db.VarChar(255)
  user_id    Int?

  @@index([prod_id], map: "prod_id")
}

model email_logs {
  id               Int       @id @default(autoincrement())
  created_at       DateTime  @default(now()) @db.Timestamp(0)
  updated_at       DateTime  @default(now()) @db.Timestamp(0)
  campaign_id      String?   @db.VarChar(255)
  recipient_email  String    @db.VarChar(255)
  recipient_note   String?   @db.Text
  subject          String    @db.VarChar(500)
  message_preview  String?   @db.Text
  status           String?   @default("pending") @db.VarChar(50)
  error_message    String?   @db.Text
  attempts         Int?      @default(0)
  last_attempt_at  DateTime? @db.Timestamp(0)
  sent_at          DateTime? @db.Timestamp(0)
  batch_number     Int?      @default(0)
  checkpoint_index Int?      @default(0)
  user_id          Int?
  user_name        String?   @db.VarChar(255)

  @@index([campaign_id], map: "idx_campaign_id")
  @@index([created_at], map: "idx_created_at")
  @@index([recipient_email], map: "idx_recipient_email")
  @@index([status], map: "idx_status")
}

model processed_emails {
  id             Int      @id @default(autoincrement())
  message_id     String   @unique @db.VarChar(500)
  from_email     String   @db.VarChar(255)
  subject        String   @db.VarChar(1000)
  sent_to_mastra Boolean  @default(false)
  processed_at   DateTime @default(now()) @db.Timestamp(0)

  @@index([message_id], map: "idx_processed_emails_message_id")
  @@index([processed_at], map: "idx_processed_emails_processed_at")
}
