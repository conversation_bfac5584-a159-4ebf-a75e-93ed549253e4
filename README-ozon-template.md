# Скрипт для заполнения шаблона Ozon

Этот скрипт автоматически заполняет шаблон Excel для загрузки товаров в Ozon на основе данных из базы данных Prisma.

## Файлы

- `ozon-template-filler.js` - **ОСНОВНОЙ СКРИПТ** (простой и топорный, как требовалось)
- `run-ozon-template.js` - альтернативная версия
- `fill-ozon-template.ts` - TypeScript версия с типами
- `ozon-template-types.ts` - типы для TypeScript
- `analyze-excel-simple.js` - скрипт для анализа структуры Excel файла
- `test-products.js` - скрипт для проверки товаров в базе

## Быстрый старт

1. Убедитесь, что файл `ozon_template.xlsx` находится в корне проекта
2. Проверьте товары в базе:

```bash
node test-products.js
```

3. Запустите основной скрипт с ID товаров:

```bash
node ozon-template-filler.js 1,2,3,4,5
```

или для одного товара:

```bash
node ozon-template-filler.js 123
```

## Что делает скрипт

1. **Получает ID товаров** из аргументов командной строки
2. **Загружает товары** из базы данных по указанным ID (поле `prod_id`)
3. **Загружает шаблон** Excel (`ozon_template.xlsx`)
4. **Заполняет данные** в лист "Шаблон" начиная с 5-й строки
5. **Сохраняет результат** в новый файл с временной меткой

## Маппинг полей

Скрипт автоматически преобразует данные из таблицы `products`:

### Обязательные поля (помечены * в шаблоне)

- **Артикул*** ← `prod_sku`
- **Цена, руб.*** ← `prod_price`
- **НДС, %*** ← фиксированное значение 20%
- **Вес в упаковке, г*** ← `prod_weight` (переводится из кг в граммы)
- **Ширина упаковки, мм*** ← `size_in` (переводится из см в мм)
- **Высота упаковки, мм*** ← `size_h` (переводится из см в мм)
- **Длина упаковки, мм*** ← `size_out` (переводится из см в мм)
- **Ссылка на главное фото*** ← первое изображение из `prod_images`

### Дополнительные поля

- **Название товара** ← `prod_note`
- **Рассрочка** ← "Нет" (по умолчанию)
- **Баллы за отзывы** ← "Да" (по умолчанию)
- **Дополнительные фото** ← остальные изображения из `prod_images` (до 13 штук)

## Структура данных

### Поле `prod_images`
Ожидается JSON массив с URL изображений:
```json
["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
```

### Размеры товара
- `size_in` - ширина в см
- `size_h` - высота в см  
- `size_out` - длина в см

Скрипт автоматически переводит в миллиметры для шаблона Ozon.

### Вес товара
- `prod_weight` - вес в кг

Скрипт автоматически переводит в граммы для шаблона Ozon.

## Значения по умолчанию

Если данные отсутствуют, используются безопасные значения:

- Вес: 100 г
- Размеры: 100×100×100 мм
- НДС: 20%
- Рассрочка: "Нет"
- Баллы за отзывы: "Да"

## Примеры использования

### Один товар
```bash
node ozon-template-filler.js 42
```

### Несколько товаров
```bash
node ozon-template-filler.js 1,5,10,15,20
```

### Большой список товаров
```bash
node ozon-template-filler.js 100,101,102,103,104,105,106,107,108,109,110
```

## Результат

Скрипт создает новый Excel файл с именем вида:
```
ozon_filled_2025-09-08T08-57-14.xlsx
```

Где дата и время указывают на момент создания файла.

## Логи

Скрипт выводит подробную информацию о процессе:

```
🚀 Заполняем шаблон Ozon для товаров: 7416, 7443, 7478
📦 Загружаем товары из базы данных...
✅ Найдено 3 товаров в базе
📄 Загружаем шаблон Excel...
📝 Заполняем данные...
  1/3: RK8005
  2/3: RK8006
  3/3: RK8007
💾 Сохраняем файл...
✅ Готово! Файл сохранен: ./ozon_filled_2025-09-08T08-57-14.xlsx
📊 Статистика:
   Успешно обработано: 3
   С ошибками: 0
   Всего товаров: 3
```

## Возможные ошибки

- **"Товары не найдены в базе данных"** - проверьте правильность ID товаров
- **"Лист 'Шаблон' не найден"** - проверьте, что используется правильный шаблон Ozon
- **"Не удалось распарсить изображения"** - поле `prod_images` содержит некорректный JSON

## Дополнительные возможности

### Очистка HTML из названий
Скрипт автоматически очищает HTML теги из названий товаров (`prod_note`):
- Убирает все HTML теги (`<br>`, `<p>`, и т.д.)
- Заменяет `&nbsp;` на пробелы
- Ограничивает длину до 500 символов

### Валидация данных
Скрипт проверяет обязательные поля и выводит предупреждения при ошибках:
- Артикул не пустой
- Цена больше 0
- Вес больше 0
- Размеры больше 0

### Статистика обработки
В конце работы выводится подробная статистика:
- Количество успешно обработанных товаров
- Количество товаров с ошибками
- Общее количество товаров

## Доработка

Скрипт написан максимально просто для одноразового использования. При необходимости можно:

1. Добавить больше валидаций данных
2. Настроить маппинг полей под конкретные нужды
3. Добавить обработку дополнительных характеристик товара
4. Реализовать пакетную обработку больших объемов данных
5. Добавить поддержку других листов шаблона (видео, документы и т.д.)
