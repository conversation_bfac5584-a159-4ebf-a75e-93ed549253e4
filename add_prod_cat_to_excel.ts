import { PrismaClient } from '@prisma/client'
import XLSX from 'xlsx'

const prisma = new PrismaClient()
const inputFilename = './data/3updated_prices.xlsx'
const outputFilename = './data/3updated_prices_with_cat.xlsx'

type ProductDataRow = {
  prod_id: number | string;
  арт: string;
  оем: string;
  'стар.скид': number;
  'нов.скид': number;
  'изнач.опт.цена': number;
  'изнач.розн.цена': number;
  'промеж.опт.цена': number;
  'промеж.розн.цена': number;
  'новая.опт.цена': number;
  'нов.розн.цена': number;
  prod_cat?: string; // Новый столбец
};

type ProductFromDB = {
  prod_id: number;
  prod_cat: string;
};

async function main(): Promise<void> {
  try {
    console.log('Читаем исходный файл...')
    const workbook = XLSX.readFile(inputFilename)
    const sheetName = Object.keys(workbook.Sheets)[0]
    const itemsData: ProductDataRow[] = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName])

    console.log(`Найдено ${itemsData.length} товаров в файле`)
    console.log('Пример первой строки:', itemsData[0])

    // Получаем все prod_id из файла
    const prodIds = itemsData
      .map(item => Number(item.prod_id))
      .filter(id => !isNaN(id) && id > 0)

    console.log(`Получаем категории для ${prodIds.length} товаров из базы данных...`)

    // Получаем prod_cat для всех товаров одним запросом
    const productsFromDB: ProductFromDB[] = await prisma.products.findMany({
      where: {
        prod_id: {
          in: prodIds
        }
      },
      select: {
        prod_id: true,
        prod_cat: true
      }
    })

    console.log(`Найдено ${productsFromDB.length} товаров в базе данных`)

    // Создаем Map для быстрого поиска категорий по prod_id
    const prodCatMap = new Map<number, string>()
    productsFromDB.forEach(product => {
      prodCatMap.set(product.prod_id, product.prod_cat)
    })

    // Добавляем prod_cat к каждому товару
    let foundCategories = 0
    const updatedItemsData = itemsData.map(item => {
      const prodId = Number(item.prod_id)
      const prodCat = prodCatMap.get(prodId)
      
      if (prodCat !== undefined) {
        foundCategories++
        return {
          ...item,
          prod_cat: prodCat
        }
      } else {
        console.warn(`Категория не найдена для товара prod_id=${prodId}, арт=${item.арт}`)
        return {
          ...item,
          prod_cat: '' // Пустая строка для товаров без категории
        }
      }
    })

    console.log(`Категории найдены для ${foundCategories} товаров`)

    // Создаем новый workbook с обновленными данными
    const newWorkbook = XLSX.utils.book_new()
    const newWorksheet = XLSX.utils.json_to_sheet(updatedItemsData)
    XLSX.utils.book_append_sheet(newWorkbook, newWorksheet, sheetName)

    // Сохраняем файл
    console.log(`Сохраняем обновленный файл: ${outputFilename}`)
    XLSX.writeFile(newWorkbook, outputFilename)

    console.log('\nГотово! Файл успешно создан с добавленным столбцом prod_cat')
    console.log(`Исходный файл: ${inputFilename}`)
    console.log(`Новый файл: ${outputFilename}`)
    console.log(`Всего товаров: ${itemsData.length}`)
    console.log(`Категории найдены: ${foundCategories}`)
    console.log(`Категории не найдены: ${itemsData.length - foundCategories}`)

  } catch (error) {
    console.error('Ошибка при выполнении скрипта:', error)
    throw error
  }
}

main()
  .catch((error) => {
    console.error('Критическая ошибка:', error)
    process.exit(1)
  })
  .finally(async () => {
    console.log('Отключаемся от базы данных...')
    await prisma.$disconnect()
  })