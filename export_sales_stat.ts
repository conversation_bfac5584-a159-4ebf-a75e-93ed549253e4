import * as XLSX from 'xlsx'
import { PrismaClient, orders, orders_snapshots } from '@prisma/client'

const prisma = new PrismaClient()
const _months = ['Январь', 'Февраль', 'Март', 'Апрель', 'Май', 'Июнь', 'Июль', 'Август', 'Сентябрь', 'Октябрь', 'Ноябрь', 'Декабрь']

interface Order extends orders {
  snap: orders_snapshots
}

async function start({ year, price = 'archive' }: { year?: number; price?: 'current' | 'archive' }) {
  const currentYear = year || new Date().getFullYear()

  const ordersByMonth: any = {}
  const results = {}

  for (const monthName of _months) {
    const monthIndex = _months.indexOf(monthName)

    const start = new Date(currentYear, monthIndex, 1)
    const end = new Date(currentYear, monthIndex + 1, 0)

    const orders = await prisma.orders.findMany({
      where: {
        order_datetime: {
          gte: start,
          lt: end
        }
      }
    })

    ordersByMonth[monthName] = orders
  }

  if (price == 'current') {
        for await (const monthName of _months) {
          console.log('forawait ~ monthName:', monthName)
          const orders = ordersByMonth[monthName]

          await Promise.all(
            orders.map(async (order: Order) => {
              const orderItems = await prisma.order_items.findMany({
                select: {
                    item_id: true,
                    item_count: true,
                },
                where: {
                  items_order_id: order.order_id
                },
              })
              const products = await prisma.products.findMany({
                select: {
                    prod_id: true,
                    prod_price: true,
                    prod_discount: true
                },
                where: {
                  prod_id: {
                    in: orderItems.map(i => i.item_id)
                  }
                }
              })

              const orderItemsWithProduct = orderItems.map((orderItem) => {
                const fp = products.find(p => p.prod_id === orderItem.item_id)
                if (fp) {
                    return {
                        ...orderItem,
                        ...fp,
                        wholesalePrices: order.order_price > 7500,
                        bigDiscount: order.order_price > 100000
                    }
                }
              })


              function calculateTotalPrice() {
                let total = 0

                for (const item of orderItemsWithProduct) {
                  if (item) {
                    let itemPrice = item.prod_price * item.item_count

                    if (item.wholesalePrices) {
                      itemPrice *= 1 - item.prod_discount
                    }

                    total += itemPrice
                  }
                }

                return total
              }

              let totalPrice = calculateTotalPrice()

              if (totalPrice > 0) {
                const applyBigDiscount = totalPrice > 100000
                if (applyBigDiscount) {
                  totalPrice *= 0.9
                }

                console.log(`Общая цена: ${totalPrice}`)
                results[monthName] = totalPrice
              } else {
                console.log('Нет товаров для подсчета.')
              }
              
            })
          )
        }


        console.log({ currentYear, price, results })

        const NWB = XLSX.utils.book_new()

        XLSX.utils.book_append_sheet(NWB, XLSX.utils.json_to_sheet([results]), `${currentYear}_${price}`)

        XLSX.writeFile(NWB, `./stats/${currentYear}_${price}.xlsx`)

        return results
  }


  if (price == 'archive') {
    Object.keys(ordersByMonth).map((month) => {
      const orders: orders[] = ordersByMonth[month]
      results[month] = orders.reduce((orderAcc, order) => {
        return orderAcc + (order.order_price + order.order_shippingprice)
      }, 0)
    })

    console.log({ currentYear, price })

    const NWB = XLSX.utils.book_new()

    XLSX.utils.book_append_sheet(NWB, XLSX.utils.json_to_sheet([results]), `${currentYear}_${price}`)

    XLSX.writeFile(NWB, `./stats/${currentYear}_${price}.xlsx`)

    return results
  }
  
}

// `${_year}-01-00`, `${_year}-12-31`
// start({ year: 2022, price: 'archive' })
// start({ year: 2022, price: 'current' })

// start({ year: 2023, price: 'archive' })
start({ year: 2023, price: 'current' })
