const { PrismaClient } = require('@prisma/client')
const XLSX = require('xlsx')

const prisma = new PrismaClient()
const _filename = './data/Для Семёна.xlsx0'

async function main() {
  const workbook_1 = XLSX.readFile(_filename)
  const itemsData = XLSX.utils.sheet_to_json(workbook_1.Sheets[Object.keys(workbook_1.Sheets)[0]])

  console.log('itemsData length:', itemsData?.length)

  const notfounds = []

  await Promise.all(
    itemsData.map(async (item) => {
      const fnd = await prisma.products.findFirst({
        select: {
          prod_id: true
        },
        where: {
          prod_analogsku: String(item.code).trim()
        }
      })

      if (!fnd) {
        notfounds.push(item.code)
      }
    })
  )

  const res = await prisma.$transaction(
    itemsData.map((item) => {
      return prisma.products.updateMany({
        data: {
          prod_count: {
            increment: Number(item['qty'])
          },
          prod_group_count: {
            increment: Number(item['qty'])
          },
          prod_purchasing: String(item.purch).replace(',', '.')
        },
        where: {
          prod_analogsku: {
            equals: String(item['code'])
          }
        }
      })
    })
  )

  console.log('res', res)

  // console.log('RES: ', res)

  console.log('notfounds: ', notfounds)
}

main()
  .catch((e) => {
    throw e
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
