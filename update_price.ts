// const { PrismaClient } = require('@prisma/client')
// const XLSX = require('xlsx')

import { PrismaClient } from '@prisma/client'

import XLSX from 'xlsx'

const prisma = new PrismaClient()
const _filename = './data/Цены 10-06-2025 доп.xlsx'
// const _filename = './data/TTO 7.xlsx'

interface FileItem {
  'Артикул(OEM)': string
  'Цена(Розн)': number
  'Скидка(%)': number
}

async function main() {
  const workbook_1 = XLSX.readFile(_filename)
  const itemsData: FileItem[] = XLSX.utils.sheet_to_json(workbook_1.Sheets[Object.keys(workbook_1.Sheets)[0]])

  const chunkSize = 1000
  const chunks = []

  for (let i = 0; i < itemsData.length; i += chunkSize) {
    chunks.push(itemsData.slice(i, i + chunkSize))
  }

  for (const chunk of chunks) {
    await prisma.$transaction(
      chunk.map((item) =>
        prisma.products.updateMany({
          data: {
            prod_price: Number(item['Цена(Розн)']),
            prod_discount: Number(item['Скидка(%)'])
          },
          where: {
            prod_analogsku: {
              equals: String(item['Артикул(OEM)'].trim())
            }
          }
        })
      )
    )
    console.log(`Processed chunk of ${chunk.length} items`)
  }
}

main()
  .catch((e) => {
    throw e
  })
  .finally(async () => {
    console.log('all done')
    await prisma.$disconnect()
  })
