[{"type": "header", "version": "5.2.1", "comment": "Export to JSON plugin for PHPMyAdmin"}, {"type": "database", "name": "prisma_test"}, {"type": "table", "name": "Translation", "database": "prisma_test", "data": [{"id": "1", "key": "Hello", "value": "Привет", "language": "ru", "includes": null}, {"id": "2", "key": "NEW_REQUEST", "value": "Hello {{author.username}! New request received from {{client.username}}", "language": "en", "includes": null}, {"id": "3", "key": "NEW_REQUEST", "value": "Здравствуйте, {{author.username}! Получен новый запрос от {{client.username}}.", "language": "ru", "includes": null}, {"id": "4", "key": "NEW_REQUEST", "value": "¡Hola {{author.username}! Nueva solicitud recibida de {{client.username}}", "language": "es", "includes": null}]}]