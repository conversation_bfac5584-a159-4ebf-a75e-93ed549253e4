const ExcelJS = require('exceljs');

async function analyzeExcelStructure() {
    console.log('Начинаем анализ Excel файла...');

    try {
        const workbook = new ExcelJS.Workbook();
        console.log('Загружаем файл...');
        await workbook.xlsx.readFile('./ozon_template.xlsx');

        console.log('=== АНАЛИЗ СТРУКТУРЫ EXCEL ФАЙЛА ===\n');

        workbook.eachSheet((worksheet, sheetId) => {
            console.log(`\n--- ЛИСТ ${sheetId}: "${worksheet.name}" ---`);
            console.log(`Размеры: ${worksheet.rowCount} строк x ${worksheet.columnCount} колонок`);

            // Анализируем первые 5 строк для понимания структуры
            for (let rowNumber = 1; rowNumber <= Math.min(5, worksheet.rowCount); rowNumber++) {
                const row = worksheet.getRow(rowNumber);
                console.log(`\nСтрока ${rowNumber}:`);

                for (let colNumber = 1; colNumber <= Math.min(10, worksheet.columnCount); colNumber++) {
                    const cell = worksheet.getCell(rowNumber, colNumber);
                    const value = cell.value;

                    if (value !== null && value !== undefined && value !== '') {
                        // Показываем значение и его тип
                        let displayValue = value;
                        if (typeof value === 'object' && value !== null) {
                            if (value.richText) {
                                displayValue = value.richText.map(rt => rt.text).join('');
                            } else {
                                displayValue = JSON.stringify(value);
                            }
                        }

                        console.log(`  ${cell.address}: "${displayValue}"`);

                        // Проверяем на обязательные поля (со звездочкой)
                        if (typeof displayValue === 'string' && displayValue.includes('*')) {
                            console.log(`    ⭐ ОБЯЗАТЕЛЬНОЕ ПОЛЕ!`);
                        }
                    }
                }
            }
        });

        console.log('\nАнализ завершен!');

    } catch (error) {
        console.error('Ошибка при анализе файла:', error.message);
        console.error('Stack:', error.stack);
    }
}

// Запускаем анализ
analyzeExcelStructure().then(() => {
    console.log('Скрипт завершен');
    process.exit(0);
}).catch(err => {
    console.error('Критическая ошибка:', err);
    process.exit(1);
});
