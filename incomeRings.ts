import { PrismaClient, products } from '@prisma/client'

import XLSX from 'xlsx'

type Item = {
  size: string
  qty: number
  price: number
  discount: number
}

const prisma = new PrismaClient()
const _filename = './data/NQK 27-10-2023.xls' //'./data/Резиновые кольца новый приход.xlsx'

function formatString(str: string) {
  const parts = str.split('*')
  const formattedParts = parts.map((part) => {
    const num = parseFloat(part)
    return num % 1 === 0 ? num.toFixed(0) : num.toFixed(1)
  })

  return formattedParts.join('*')
}

function getPriceBySize(size: string) {
  const [size_in, size_out_, size_h] = String(size).split('*')

  if (Number(size_in) > 100) {
    return 80
  }

  if (Number(size_in) > 200) {
    return 150
  }

  if (Number(size_in) <= 20) {
    return 20
  }

  if (Number(size_in) <= 50) {
    return 25
  }

  if (Number(size_in) > 50) {
    return 40
  }
}

async function main() {
  const workbook_1 = XLSX.readFile(_filename)
  const itemsData: Item[] = XLSX.utils.sheet_to_json(workbook_1.Sheets[Object.keys(workbook_1.Sheets)[0]])

  //   console.log('itemsData length:', itemsData)

  //   console.log(itemsData.map((i) => formatString(i.size)))

  const newData = itemsData.map((i) => ({ ...i, size: formatString(i.size) }))

  //   const NWB = XLSX.utils.book_new()

  //   const NWS = XLSX.utils.json_to_sheet(newData)
  //   const NWS2 = XLSX.utils.json_to_sheet(itemsData)
  //   XLSX.utils.book_append_sheet(NWB, NWS, '0')
  //   XLSX.utils.book_append_sheet(NWB, NWS2, '1')
  //   XLSX.writeFile(NWB, 'rstest.xlsx')

  let i = 0
  let skuID = 50725

  const prods = await prisma.products.findMany({
    where: {
      prod_size: {
        in: newData.map((i) => String(i.size).trim())
      },
      prod_cat: '6'
    }
  })

  const existSizes = {}

  prods.map((prod) => {
    existSizes[prod.prod_size] = true
  })

  console.log('existSizes len', Object.keys(existSizes).length)

  const notExistArray = newData.filter((i) => !existSizes[i.size])

  const newProducts: products[] = notExistArray.map((item) => {
    const [size_in, size_out, size_h] = String(item.size).split('*')

    skuID++

    return {
      prod_size: item.size,
      size_in,
      size_out,
      size_h,
      prod_price: getPriceBySize(item.size),
      prod_discount: 20,
      prod_count: 0,
      prod_composition: 'Приход колец от 27.11.23',
      prod_sku: `A${skuID}`,
      prod_analogsku: `A${skuID}`,
      prod_cat: '6',
      prod_img: 'A01659', // A01659.jpg
      prod_img_rumi: 'A01659', // A01659.jpg
      prod_manuf: 'TCS',
      prod_purpose: 'Резиновое кольцо',
      prod_material: 'NBR',
      prod_type: 'O-Ring'
    }
  })

  // console.log('newData:', newData)

  //uncom
  // return newData

  const createRes = await prisma.products.createMany({
    data: newProducts
  })

  console.log('🚀 ~ file: incomeRings.ts:111 ~ main ~ createRes:', createRes)

  const NWB = XLSX.utils.book_new()

  const NWC = XLSX.utils.json_to_sheet(newProducts)

  const NWS = XLSX.utils.json_to_sheet(notExistArray)
  const NWS2 = XLSX.utils.json_to_sheet(prods.map((i) => ({ size: i.prod_size, sku: i.prod_sku })))

  XLSX.utils.book_append_sheet(NWB, NWS, 'Не найдены')
  XLSX.utils.book_append_sheet(NWB, NWS2, 'Существующие')
  XLSX.utils.book_append_sheet(NWB, NWC, 'Созданы')

  XLSX.writeFile(NWB, 'fi271123.xlsx')

  // return false

  //   await Promise.all(
  //     newData.map(async (item) => {
  //       const prod = await prisma.products.findFirst({
  //         select: {
  //           prod_id: true
  //         },
  //         where: {
  //           prod_size: String(item.size).trim(),
  //           prod_cat: '6'
  //         }
  //       })

  //       if (!prod) {
  //         console.log('Не найден:', item.size)
  //         console.log('i:', i++)
  //       }
  //     })
  //   )

  // console.log('not founds:', i)

  // return false
  console.log('start update')

  const res = await prisma.$transaction(
    newData.map((item) => {
      return prisma.products.updateMany({
        data: {
          // prod_count: {
          //   increment: Number(item['qty'])
          // },
          // prod_composition: `Приход ${item['qty']} шт. от 27.11.23`,
          prod_price: Number(item.price),
          prod_discount: Number(item.discount)
        },
        where: {
          prod_size: {
            equals: String(item['size']).trim()
          },
          prod_cat: '6'
        }
      })
    })
  )

  console.log('RES: ', res)
}

main()
  .catch((e) => {
    throw e
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
