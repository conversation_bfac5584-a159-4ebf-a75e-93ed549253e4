import { PrismaClient, products } from '@prisma/client'
import axios from 'axios'
const prisma = new PrismaClient()

function generateNextSKU(prev) {
  let currentNumber = parseInt(prev.slice(4)) // Получаем числовую часть стартового номера
  const sku = `K01B${String(currentNumber + 1).padStart(5, '0')}` // Форматируем артикул
  return sku
}

async function createItem(data: { prod_id: number; fields: Partial<products> }) {
  return await axios.post('https://api.rti-baltika.ru/cpan/products/update', {
    prod_id: data.prod_id,
    fields: data
  })
}

let currentSku = 'K01B00000'
// const sku = generateNextSKU(prev)
// console.log(sku)

async function init() {
  const prodsToUpd = await prisma.products.findMany({
    where: {
      prod_cat: {
        in: ['31', '34']
      },
      prod_manuf: 'SNF'
    },
    orderBy: {
      prod_id: 'asc'
    }
  })

  console.log('prodsToUpd len: ', prodsToUpd.length)

  //   return false;

  const itemsData: { prod_id: number; fields: Partial<products> }[] = prodsToUpd.map((i) => {
    // currentSku = generateNextSKU(currentSku)
    return {
      prod_id: i.prod_id,
      fields: {
        prod_id: i.prod_id,
        prod_img: '',
        prod_img_rumi: '',
        // prod_sku: currentSku,
        // prod_analogsku: currentSku,
        // prod_composition: 'k251223'
      }
    }
  })

//   const prismaData: Partial<products>[] = prodsToUpd.map((i) => {
//     currentSku = generateNextSKU(currentSku)
//     // console.log('currentSku:', currentSku)
//     return {
//       prod_id: i.prod_id,
//     //   prod_img: '',
//     //   prod_img_rumi: '',
//       prod_sku: currentSku,
//       prod_analogsku: currentSku,
//       prod_composition: 'k251223'
//     }
//   })

  let i = 0
  let errors = []

//   for (const item of prismaData) {
//     const res = await prisma.products.update({
//       where: {
//         prod_id: item.prod_id
//       },
//       data: {
//         prod_sku: item.prod_sku,
//         prod_analogsku: item.prod_analogsku,
//         prod_composition: item.prod_composition
//       },
//       select: {
//         prod_id: true,
//         prod_sku: true
//       }
//     })

//     console.log('res: ', res)

//     console.log('-----')

//     console.log(`${i++} из ${prismaData.length}`)
//   }

    for (const item of itemsData) {
      console.log('current:', item.fields.prod_sku)

      const res = await createItem(item)

      console.log('res status', res.status)
      // console.log('res:', res.data?.prod_id)
      console.log('-----')

      console.log(`${i++} из ${itemsData.length}`)
    }
}

init()
