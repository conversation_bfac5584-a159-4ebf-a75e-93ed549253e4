
const { PrismaClient } = require('@prisma/client')
const XLSX = require("xlsx");


const prisma = new PrismaClient()
const _filename = './data/ДЛЯ загрузки МАТЕРИАЛЫ(1).xlsx'


async function main() {

    const workbook_1 = XLSX.readFile(_filename)
    const itemsData = XLSX.utils.sheet_to_json(workbook_1.Sheets[Object.keys(workbook_1.Sheets)[0]])
    
    console.log('itemsData length:', itemsData?.length)

    for (const item of itemsData) {
        console.log('start: ', item)
        const res = await prisma.products.updateMany({
            data: {
                prod_material: item['Будет']
            },
            where: {
                prod_material: {
                    equals: String(item['Сейчас'])
                }
            }
        })
        console.log('succes: ', res)
    }
}

main()
  .catch((e) => {
    throw e
  })
  .finally(async () => {
    await prisma.$disconnect()
  })