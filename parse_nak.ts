import { PrismaClient } from '@prisma/client'

import XLSX from 'xlsx'

const prisma = new PrismaClient()
const _filename = './NAK_разд.xlsx'

interface FileItem {
  code: string
  size: string
  type: string
  material: string
  qty: number
  isDM?: number | null | undefined
  size_dm?: string | null | undefined
}

function inchesToMm(str = '') {
  const regex = /\d+(?:\.\d+)?/g

  return str.replace(regex, (match) => {
    let res = Number(match) * 25.4
    // return res.toFixed(1) //.toFixed(Number.isInteger(match) ? 0 : 1)

    return Number.isInteger(Number(res.toFixed(1))) ? res.toFixed(0) : res.toFixed(1)
  })
}

function mmToInches(str = '') {
  const regex = /\d+(?:\.\d+)?/g

  return str.replace(regex, (match) => {
    let res = Number(match) / 25.4
    return res.toFixed(Number.isInteger(match) ? 0 : 3)
  })
}

async function main() {
  const workbook_1 = XLSX.readFile(_filename)
  const itemsData: FileItem[] = XLSX.utils.sheet_to_json(workbook_1.Sheets[Object.keys(workbook_1.Sheets)[0]])

  itemsData.forEach((item) => {
    if (item.isDM) {
      item.size = inchesToMm(item.size_dm ?? '')
    } else {
      item.size_dm = mmToInches(item.size ?? '')
    }
  })

  //   console.log("itemsData", itemsData)
  console.log('itemsData length:', itemsData?.length)

  console.log('load products...')

  const products = await prisma.products.findMany({
    select: {
      prod_size: true,
      prod_type: true,
      prod_material: true
    },
    where: {
      prod_count: {
        gt: 0
      }
    }
  })

  console.log('products len: ', products.length)

  //   const newProducts = products.filter((p) => !itemsData.find((i) => i.size == p.prod_size && i.type == p.prod_type && i.material == p.prod_material))

  const newItems = itemsData.filter((i) => !products.find((p) => i.size == p.prod_size && i.type == p.prod_type && i.material == p.prod_material))

  console.log('🚀 ~ main ~ newItems:', newItems.length)

  console.log('start write file...')

  const NWB = XLSX.utils.book_new()

  XLSX.utils.book_append_sheet(NWB, XLSX.utils.json_to_sheet(newItems), '1')

  XLSX.writeFile(NWB, 'parsenak_result.xlsx')

  console.log('done.')
}

main()
