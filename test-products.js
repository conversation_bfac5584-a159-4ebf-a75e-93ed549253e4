const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testProducts() {
  try {
    console.log('🔍 Проверяем товары в базе данных...');
    
    // Получаем первые 10 товаров для тестирования
    const products = await prisma.products.findMany({
      take: 10,
      select: {
        prod_id: true,
        prod_sku: true,
        prod_note: true,
        prod_price: true,
        prod_weight: true,
        prod_images: true,
        size_in: true,
        size_out: true,
        size_h: true
      }
    });

    console.log(`✅ Найдено товаров в базе: ${products.length}`);
    
    if (products.length > 0) {
      console.log('\n📦 Примеры товаров:');
      products.forEach((product, index) => {
        console.log(`\n${index + 1}. ID: ${product.prod_id}`);
        console.log(`   SKU: ${product.prod_sku}`);
        console.log(`   Название: ${product.prod_note?.substring(0, 50)}...`);
        console.log(`   Цена: ${product.prod_price}`);
        console.log(`   Вес: ${product.prod_weight}`);
        console.log(`   Размеры: ${product.size_in}×${product.size_out}×${product.size_h}`);
        
        // Проверяем изображения
        if (product.prod_images) {
          try {
            const images = JSON.parse(product.prod_images);
            console.log(`   Изображений: ${Array.isArray(images) ? images.length : 'некорректный формат'}`);
          } catch (e) {
            console.log(`   Изображения: ошибка парсинга JSON`);
          }
        } else {
          console.log(`   Изображения: отсутствуют`);
        }
      });
      
      console.log('\n🚀 Для тестирования скрипта используйте:');
      const testIds = products.slice(0, 3).map(p => p.prod_id);
      console.log(`node run-ozon-template.js ${testIds.join(',')}`);
    }
    
  } catch (error) {
    console.error('❌ Ошибка:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testProducts();
