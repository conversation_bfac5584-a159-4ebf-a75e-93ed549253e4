import { PrismaClient } from '@prisma/client';
import ExcelJS from 'exceljs';
import { OzonProductTemplate, EXCEL_COLUMN_MAPPING, REQUIRED_FIELDS } from './ozon-template-types';

const prisma = new PrismaClient();

interface FillTemplateOptions {
  /** Массив ID товаров для выборки */
  productIds: number[];
  /** Путь к исходному шаблону */
  templatePath?: string;
  /** Путь для сохранения заполненного файла */
  outputPath?: string;
}

/**
 * Заполняет шаблон Ozon данными из базы
 */
async function fillOzonTemplate(options: FillTemplateOptions) {
  const {
    productIds,
    templatePath = './ozon_template.xlsx',
    outputPath = './ozon_filled_template.xlsx'
  } = options;

  console.log(`🚀 Начинаем заполнение шаблона Ozon для ${productIds.length} товаров...`);

  try {
    // 1. Загружаем товары из базы
    console.log('📦 Загружаем товары из базы данных...');
    const products = await prisma.products.findMany({
      where: {
        prod_id: {
          in: productIds
        }
      }
    });

    console.log(`✅ Найдено ${products.length} товаров в базе`);

    if (products.length === 0) {
      throw new Error('Товары не найдены в базе данных');
    }

    // 2. Загружаем шаблон Excel
    console.log('📄 Загружаем шаблон Excel...');
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(templatePath);

    // 3. Находим лист "Шаблон"
    const worksheet = workbook.getWorksheet('Шаблон');
    if (!worksheet) {
      throw new Error('Лист "Шаблон" не найден в файле');
    }

    console.log('📝 Заполняем данные...');

    // 4. Заполняем данные для каждого товара
    products.forEach((product, index) => {
      const rowNumber = 5 + index; // Данные начинаются с 5-й строки
      
      console.log(`  Обрабатываем товар ${index + 1}/${products.length}: ${product.prod_sku}`);

      // Преобразуем данные товара в формат шаблона
      const templateData = convertProductToTemplate(product, index + 1);

      // Заполняем строку в Excel
      fillExcelRow(worksheet, rowNumber, templateData);
    });

    // 5. Сохраняем файл
    console.log('💾 Сохраняем заполненный файл...');
    await workbook.xlsx.writeFile(outputPath);

    console.log(`✅ Шаблон успешно заполнен и сохранен: ${outputPath}`);
    console.log(`📊 Обработано товаров: ${products.length}`);

  } catch (error) {
    console.error('❌ Ошибка при заполнении шаблона:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Преобразует данные товара из базы в формат шаблона Ozon
 */
function convertProductToTemplate(product: any, number: number): OzonProductTemplate {
  // Простое преобразование данных - можно доработать под конкретные нужды
  
  // Парсим изображения из JSON строки
  let images: string[] = [];
  try {
    if (product.prod_images) {
      const imageData = JSON.parse(product.prod_images);
      if (Array.isArray(imageData)) {
        images = imageData;
      }
    }
  } catch (e) {
    console.warn(`Не удалось распарсить изображения для товара ${product.prod_sku}`);
  }

  // Базовые размеры и вес (если не указаны, ставим минимальные значения)
  const weight = product.prod_weight ? parseFloat(product.prod_weight) * 1000 : 100; // в граммах
  const dimensions = {
    width: 100,   // мм
    height: 100,  // мм  
    length: 100   // мм
  };

  // Пытаемся извлечь размеры из полей size_*
  if (product.size_in && product.size_out && product.size_h) {
    dimensions.width = Math.round(parseFloat(product.size_in.toString()) * 10) || 100;
    dimensions.length = Math.round(parseFloat(product.size_out.toString()) * 10) || 100;
    dimensions.height = Math.round(parseFloat(product.size_h.toString()) * 10) || 100;
  }

  return {
    number,
    sku: product.prod_sku || `PROD_${product.prod_id}`,
    name: product.prod_note || `Товар ${product.prod_sku}`,
    price: parseFloat(product.prod_price?.toString() || '0'),
    oldPrice: undefined, // Можно вычислить на основе скидки
    vat: 20, // По умолчанию 20%
    installment: 'Нет',
    reviewPoints: 'Да',
    ozonSku: undefined,
    barcode: undefined,
    weight: Math.round(weight),
    width: dimensions.width,
    height: dimensions.height,
    length: dimensions.length,
    mainImageUrl: images[0] || '', // Первое изображение как главное
    imageUrl2: images[1],
    imageUrl3: images[2],
    imageUrl4: images[3],
    imageUrl5: images[4],
    imageUrl6: images[5],
    imageUrl7: images[6],
    imageUrl8: images[7],
    imageUrl9: images[8],
    imageUrl10: images[9],
    imageUrl11: images[10],
    imageUrl12: images[11],
    imageUrl13: images[12],
    imageUrl14: images[13],
  };
}

/**
 * Заполняет строку в Excel данными товара
 */
function fillExcelRow(worksheet: ExcelJS.Worksheet, rowNumber: number, data: OzonProductTemplate) {
  // Простое заполнение по колонкам
  const row = worksheet.getRow(rowNumber);
  
  // Заполняем основные поля
  row.getCell('A').value = data.number;
  row.getCell('B').value = data.sku;
  row.getCell('C').value = data.name;
  row.getCell('D').value = data.price;
  row.getCell('E').value = data.oldPrice;
  row.getCell('F').value = data.vat;
  row.getCell('G').value = data.installment;
  row.getCell('H').value = data.reviewPoints;
  row.getCell('I').value = data.ozonSku;
  row.getCell('J').value = data.barcode;
  row.getCell('K').value = data.weight;
  row.getCell('L').value = data.width;
  row.getCell('M').value = data.height;
  row.getCell('N').value = data.length;
  row.getCell('O').value = data.mainImageUrl;
  
  // Заполняем дополнительные изображения
  row.getCell('P').value = data.imageUrl2;
  row.getCell('Q').value = data.imageUrl3;
  row.getCell('R').value = data.imageUrl4;
  row.getCell('S').value = data.imageUrl5;
  row.getCell('T').value = data.imageUrl6;
  row.getCell('U').value = data.imageUrl7;
  row.getCell('V').value = data.imageUrl8;
  row.getCell('W').value = data.imageUrl9;
  row.getCell('X').value = data.imageUrl10;
  row.getCell('Y').value = data.imageUrl11;
  row.getCell('Z').value = data.imageUrl12;
  row.getCell('AA').value = data.imageUrl13;
  row.getCell('AB').value = data.imageUrl14;

  row.commit();
}

/**
 * Валидирует данные товара
 */
function validateTemplateData(data: OzonProductTemplate): string[] {
  const errors: string[] = [];

  // Проверяем обязательные поля
  REQUIRED_FIELDS.forEach(field => {
    if (!data[field as keyof OzonProductTemplate]) {
      errors.push(`Отсутствует обязательное поле: ${field}`);
    }
  });

  // Дополнительные проверки
  if (data.price <= 0) {
    errors.push('Цена должна быть больше 0');
  }

  if (data.weight <= 0) {
    errors.push('Вес должен быть больше 0');
  }

  return errors;
}

// Экспорт для использования в других файлах
export { fillOzonTemplate, convertProductToTemplate, validateTemplateData };

// Если файл запускается напрямую
if (require.main === module) {
  // Пример использования
  const productIds = [1, 2, 3]; // Замените на реальные ID товаров
  
  fillOzonTemplate({ productIds })
    .then(() => {
      console.log('🎉 Готово!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Ошибка:', error);
      process.exit(1);
    });
}
