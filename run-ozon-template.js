/**
 * Простой скрипт для заполнения шаблона Ozon
 * 
 * Использование:
 * node run-ozon-template.js [id1,id2,id3...]
 * 
 * Примеры:
 * node run-ozon-template.js 1,2,3,4,5
 * node run-ozon-template.js 100
 */

const { PrismaClient } = require('@prisma/client');
const XLSX = require('xlsx');

const prisma = new PrismaClient();

// Получаем ID товаров из аргументов командной строки
const args = process.argv.slice(2);
let productIds = [];

if (args.length === 0) {
  console.log('❌ Не указаны ID товаров!');
  console.log('');
  console.log('Использование:');
  console.log('  node run-ozon-template.js 1,2,3,4,5');
  console.log('  node run-ozon-template.js 100');
  console.log('');
  process.exit(1);
}

// Парсим ID товаров
try {
  const idsString = args[0];
  productIds = idsString.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
  
  if (productIds.length === 0) {
    throw new Error('Не удалось распарсить ID товаров');
  }
} catch (error) {
  console.log('❌ Ошибка при парсинге ID товаров:', error.message);
  console.log('Пример: node run-ozon-template.js 1,2,3,4,5');
  process.exit(1);
}

console.log(`🚀 Заполняем шаблон Ozon для товаров: ${productIds.join(', ')}`);

/**
 * Преобразует товар из базы в данные для шаблона
 */
function convertProduct(product, index) {
  // Парсим изображения
  let images = [];
  try {
    if (product.prod_images) {
      const imageData = JSON.parse(product.prod_images);
      if (Array.isArray(imageData)) {
        images = imageData;
      }
    }
  } catch (e) {
    console.warn(`⚠️  Не удалось распарсить изображения для товара ${product.prod_sku}`);
  }

  // Вес в граммах
  let weight = 100; // по умолчанию
  if (product.prod_weight) {
    const weightKg = parseFloat(product.prod_weight);
    if (!isNaN(weightKg)) {
      weight = Math.round(weightKg * 1000);
    }
  }

  // Размеры в мм
  let width = 100, height = 100, length = 100;
  
  if (product.size_in) {
    const w = parseFloat(product.size_in) * 10; // переводим см в мм
    if (!isNaN(w) && w > 0) width = Math.round(w);
  }
  
  if (product.size_h) {
    const h = parseFloat(product.size_h) * 10;
    if (!isNaN(h) && h > 0) height = Math.round(h);
  }
  
  if (product.size_out) {
    const l = parseFloat(product.size_out) * 10;
    if (!isNaN(l) && l > 0) length = Math.round(l);
  }

  // Цена
  let price = 0;
  if (product.prod_price) {
    price = parseFloat(product.prod_price);
    if (isNaN(price)) price = 0;
  }

  return {
    number: index + 1,
    sku: product.prod_sku || `PROD_${product.prod_id}`,
    name: product.prod_note || `Товар ${product.prod_sku}`,
    price: price,
    oldPrice: null,
    vat: 20, // НДС 20%
    installment: 'Нет',
    reviewPoints: 'Да',
    ozonSku: null,
    barcode: null,
    weight: weight,
    width: width,
    height: height,
    length: length,
    mainImageUrl: images[0] || '',
    images: images.slice(1, 14) // остальные изображения
  };
}

/**
 * Заполняет строку в Excel (XLSX версия)
 */
function fillRow(worksheet, rowNumber, data) {
  // Заполняем основные поля
  worksheet[`A${rowNumber}`] = { v: data.number };
  worksheet[`B${rowNumber}`] = { v: data.sku };
  worksheet[`C${rowNumber}`] = { v: data.name };
  worksheet[`D${rowNumber}`] = { v: data.price };
  if (data.oldPrice) worksheet[`E${rowNumber}`] = { v: data.oldPrice };
  worksheet[`F${rowNumber}`] = { v: data.vat };
  worksheet[`G${rowNumber}`] = { v: data.installment };
  worksheet[`H${rowNumber}`] = { v: data.reviewPoints };
  if (data.ozonSku) worksheet[`I${rowNumber}`] = { v: data.ozonSku };
  if (data.barcode) worksheet[`J${rowNumber}`] = { v: data.barcode };
  worksheet[`K${rowNumber}`] = { v: data.weight };
  worksheet[`L${rowNumber}`] = { v: data.width };
  worksheet[`M${rowNumber}`] = { v: data.height };
  worksheet[`N${rowNumber}`] = { v: data.length };
  worksheet[`O${rowNumber}`] = { v: data.mainImageUrl };

  // Дополнительные изображения
  const imageCells = ['P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB'];
  data.images.forEach((imageUrl, index) => {
    if (index < imageCells.length && imageUrl) {
      worksheet[`${imageCells[index]}${rowNumber}`] = { v: imageUrl };
    }
  });
}

/**
 * Основная функция
 */
async function main() {
  try {
    // 1. Загружаем товары из базы
    console.log('📦 Загружаем товары из базы данных...');
    const products = await prisma.products.findMany({
      where: {
        prod_id: {
          in: productIds
        }
      }
    });

    console.log(`✅ Найдено ${products.length} товаров в базе`);

    if (products.length === 0) {
      console.log('❌ Товары не найдены в базе данных');
      return;
    }

    // 2. Загружаем шаблон
    console.log('📄 Загружаем шаблон Excel...');
    const workbook = XLSX.readFile('./ozon_template.xlsx');

    const worksheet = workbook.Sheets['Шаблон'];
    if (!worksheet) {
      throw new Error('Лист "Шаблон" не найден');
    }

    // 3. Заполняем данные
    console.log('📝 Заполняем данные...');
    products.forEach((product, index) => {
      console.log(`  ${index + 1}/${products.length}: ${product.prod_sku}`);
      
      const templateData = convertProduct(product, index);
      const rowNumber = 5 + index; // Данные начинаются с 5-й строки
      
      fillRow(worksheet, rowNumber, templateData);
    });

    // 4. Обновляем диапазон листа
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:BA100');
    const lastRow = Math.max(range.e.r, 4 + products.length);
    worksheet['!ref'] = XLSX.utils.encode_range({
      s: { c: 0, r: 0 },
      e: { c: range.e.c, r: lastRow }
    });

    // 5. Сохраняем файл
    const outputPath = `./ozon_filled_${Date.now()}.xlsx`;
    console.log('💾 Сохраняем файл...');
    XLSX.writeFile(workbook, outputPath);

    console.log(`✅ Готово! Файл сохранен: ${outputPath}`);
    console.log(`📊 Обработано товаров: ${products.length}`);

  } catch (error) {
    console.error('❌ Ошибка:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// Запускаем
main();
